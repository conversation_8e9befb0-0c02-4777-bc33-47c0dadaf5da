(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{1362:(e,t,r)=>{"use strict";r.d(t,{D:()=>c,N:()=>d});var s=r(2115),n=(e,t,r,s,n,a,o,i)=>{let l=document.documentElement,c=["light","dark"];function d(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,s=r&&a?n.map(e=>a[e]||e):n;r?(l.classList.remove(...s),l.classList.add(a&&a[t]?a[t]:t)):l.setAttribute(e,t)}),r=t,i&&c.includes(r)&&(l.style.colorScheme=r)}if(s)d(s);else try{let e=localStorage.getItem(t)||r,s=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(s)}catch(e){}},a=["light","dark"],o="(prefers-color-scheme: dark)",i=s.createContext(void 0),l={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=s.useContext(i))?e:l},d=e=>s.useContext(i)?s.createElement(s.Fragment,null,e.children):s.createElement(m,{...e}),u=["light","dark"],m=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:n=!0,enableColorScheme:l=!0,storageKey:c="theme",themes:d=u,defaultTheme:m=n?"system":"light",attribute:x="data-theme",value:b,children:y,nonce:v,scriptProps:w}=e,[k,j]=s.useState(()=>f(c,m)),[N,C]=s.useState(()=>"system"===k?p():k),S=b?Object.values(b):d,E=s.useCallback(e=>{let t=e;if(!t)return;"system"===e&&n&&(t=p());let s=b?b[t]:t,o=r?g(v):null,i=document.documentElement,c=e=>{"class"===e?(i.classList.remove(...S),s&&i.classList.add(s)):e.startsWith("data-")&&(s?i.setAttribute(e,s):i.removeAttribute(e))};if(Array.isArray(x)?x.forEach(c):c(x),l){let e=a.includes(m)?m:null,r=a.includes(t)?t:e;i.style.colorScheme=r}null==o||o()},[v]),A=s.useCallback(e=>{let t="function"==typeof e?e(k):e;j(t);try{localStorage.setItem(c,t)}catch(e){}},[k]),T=s.useCallback(e=>{C(p(e)),"system"===k&&n&&!t&&E("system")},[k,t]);s.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(T),T(e),()=>e.removeListener(T)},[T]),s.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?j(e.newValue):A(m))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[A]),s.useEffect(()=>{E(null!=t?t:k)},[t,k]);let I=s.useMemo(()=>({theme:k,setTheme:A,forcedTheme:t,resolvedTheme:"system"===k?N:k,themes:n?[...d,"system"]:d,systemTheme:n?N:void 0}),[k,A,t,N,n,d]);return s.createElement(i.Provider,{value:I},s.createElement(h,{forcedTheme:t,storageKey:c,attribute:x,enableSystem:n,enableColorScheme:l,defaultTheme:m,value:b,themes:d,nonce:v,scriptProps:w}),y)},h=s.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:o,enableColorScheme:i,defaultTheme:l,value:c,themes:d,nonce:u,scriptProps:m}=e,h=JSON.stringify([a,r,l,t,d,c,o,i]).slice(1,-1);return s.createElement("script",{...m,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(n.toString(),")(").concat(h,")")}})}),f=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},g=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},p=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},2098:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2643:(e,t,r)=>{"use strict";r.d(t,{U:()=>n});var s=r(3865);function n(){return(0,s.createBrowserClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxb2l4dmdtcm9lamdhZWJ4eWVxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNjU0ODksImV4cCI6MjA2Njc0MTQ4OX0.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU")}},3430:(e,t,r)=>{Promise.resolve().then(r.bind(r,9690))},3509:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},6688:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>l});var s=r(5155),n=r(2115),a=r(2098),o=r(3509),i=r(1362);function l(){let{theme:e,setTheme:t}=(0,i.D)(),[r,l]=n.useState(!1);return(n.useEffect(()=>{l(!0)},[]),r)?(0,s.jsxs)("button",{onClick:()=>t("light"===e?"dark":"light"),className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:["light"===e?(0,s.jsx)(o.A,{className:"h-[1.2rem] w-[1.2rem]"}):(0,s.jsx)(a.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]}):(0,s.jsxs)("button",{className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:[(0,s.jsx)(a.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]})}},7394:(e,t,r)=>{"use strict";r.d(t,{ClientThemeToggle:()=>a});var s=r(5155),n=r(6688);function a(){return(0,s.jsx)(n.ThemeToggle,{})}},9690:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(5155),n=r(2115),a=r(2643),o=r(5695),i=r(6874),l=r.n(i),c=r(7394);function d(){let[e,t]=(0,n.useState)(""),[r,i]=(0,n.useState)(""),[d,u]=(0,n.useState)(!1),[m,h]=(0,n.useState)(""),[f,g]=(0,n.useState)(!1),p=(0,o.useRouter)(),x=(0,a.U)(),b=async t=>{t.preventDefault(),u(!0),h("");try{if(f){let{error:t}=await x.auth.signUp({email:e,password:r});if(t)throw t;h("Registration successful! Please check your email to verify your account.")}else{let{error:t}=await x.auth.signInWithPassword({email:e,password:r});if(t)throw t;p.push("/"),p.refresh()}}catch(e){h(e instanceof Error?e.message:"An error occurred")}finally{u(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("header",{className:"bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(l(),{href:"/",className:"text-xl sm:text-2xl font-bold text-foreground hover:text-primary transition-colors",children:"My Blog"}),(0,s.jsx)(c.ClientThemeToggle,{})]})})}),(0,s.jsx)("div",{className:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-6 bg-primary/10 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),(0,s.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-foreground mb-2",children:f?"Create your account":"Welcome back"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:f?"Join our community today":"Sign in to your account"}),(0,s.jsxs)("p",{className:"mt-4 text-sm text-muted-foreground",children:["Or"," ",(0,s.jsx)(l(),{href:"/",className:"font-medium text-primary hover:text-primary/80 transition-colors",children:"return to home"})]})]}),(0,s.jsx)("div",{className:"bg-card rounded-xl shadow-lg border border-border p-6 sm:p-8",children:(0,s.jsxs)("form",{className:"space-y-6",onSubmit:b,children:[(0,s.jsxs)("div",{className:"space-y-5",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-foreground mb-2",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"w-full px-4 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground",placeholder:"Enter your email address",value:e,onChange:e=>t(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-foreground mb-2",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"w-full px-4 py-3 border border-input bg-background text-foreground rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground",placeholder:"Enter your password",value:r,onChange:e=>i(e.target.value)})]})]}),m&&(0,s.jsx)("div",{className:"text-sm text-center p-4 rounded-lg ".concat(m.includes("successful")?"bg-green-50 text-green-700 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800":"bg-destructive/10 text-destructive border border-destructive/20"),children:m}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:d,className:"w-full bg-primary text-primary-foreground py-3 px-4 rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md",children:d?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"}),"Processing..."]}):f?"Create Account":"Sign In"})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("button",{type:"button",onClick:()=>g(!f),className:"text-primary hover:text-primary/80 text-sm transition-colors font-medium",suppressHydrationWarning:!0,children:f?"Already have an account? Sign in":"Don't have an account? Sign up"})})]})})]})})]})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(2115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:d="",children:u,iconNode:m,...h}=e;return(0,s.createElement)("svg",{ref:t,...c,width:n,height:n,stroke:r,strokeWidth:o?24*Number(a)/Number(n):a,className:i("lucide",d),...!u&&!l(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,s.forwardRef)((r,a)=>{let{className:l,...c}=r;return(0,s.createElement)(d,{ref:a,iconNode:t,className:i("lucide-".concat(n(o(e))),"lucide-".concat(e),l),...c})});return r.displayName=o(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,271,441,684,358],()=>t(3430)),_N_E=e.O()}]);