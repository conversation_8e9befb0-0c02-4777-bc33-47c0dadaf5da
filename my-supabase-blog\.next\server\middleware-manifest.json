{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "InWAx9TKRw0SwjwgHaN9o9iJ2f59fSsKSswH0rIg4hk=", "__NEXT_PREVIEW_MODE_ID": "6b0d238e25a6ed76e3a8fa9345464b36", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c3772f019c950d9179a4ed3c2bf6898cd03a04a93f32f3ec78bedba9eebfb56e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "406ff1de3407c244c817be707b6771cc4c0eec1f08edf4a886a58306545be6aa"}}}, "sortedMiddleware": ["/"], "functions": {}}