(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[578],{419:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,7339)),Promise.resolve().then(r.bind(r,6688))},1362:(e,t,r)=>{"use strict";r.d(t,{D:()=>d,N:()=>c});var s=r(2115),n=(e,t,r,s,n,a,o,i)=>{let l=document.documentElement,d=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,s=r&&a?n.map(e=>a[e]||e):n;r?(l.classList.remove(...s),l.classList.add(a&&a[t]?a[t]:t)):l.setAttribute(e,t)}),r=t,i&&d.includes(r)&&(l.style.colorScheme=r)}if(s)c(s);else try{let e=localStorage.getItem(t)||r,s=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(s)}catch(e){}},a=["light","dark"],o="(prefers-color-scheme: dark)",i=s.createContext(void 0),l={setTheme:e=>{},themes:[]},d=()=>{var e;return null!=(e=s.useContext(i))?e:l},c=e=>s.useContext(i)?s.createElement(s.Fragment,null,e.children):s.createElement(h,{...e}),m=["light","dark"],h=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:n=!0,enableColorScheme:l=!0,storageKey:d="theme",themes:c=m,defaultTheme:h=n?"system":"light",attribute:g="data-theme",value:v,children:j,nonce:b,scriptProps:y}=e,[k,w]=s.useState(()=>x(d,h)),[N,L]=s.useState(()=>"system"===k?f():k),C=v?Object.values(v):c,S=s.useCallback(e=>{let t=e;if(!t)return;"system"===e&&n&&(t=f());let s=v?v[t]:t,o=r?p(b):null,i=document.documentElement,d=e=>{"class"===e?(i.classList.remove(...C),s&&i.classList.add(s)):e.startsWith("data-")&&(s?i.setAttribute(e,s):i.removeAttribute(e))};if(Array.isArray(g)?g.forEach(d):d(g),l){let e=a.includes(h)?h:null,r=a.includes(t)?t:e;i.style.colorScheme=r}null==o||o()},[b]),M=s.useCallback(e=>{let t="function"==typeof e?e(k):e;w(t);try{localStorage.setItem(d,t)}catch(e){}},[k]),A=s.useCallback(e=>{L(f(e)),"system"===k&&n&&!t&&S("system")},[k,t]);s.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(A),A(e),()=>e.removeListener(A)},[A]),s.useEffect(()=>{let e=e=>{e.key===d&&(e.newValue?w(e.newValue):M(h))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[M]),s.useEffect(()=>{S(null!=t?t:k)},[t,k]);let E=s.useMemo(()=>({theme:k,setTheme:M,forcedTheme:t,resolvedTheme:"system"===k?N:k,themes:n?[...c,"system"]:c,systemTheme:n?N:void 0}),[k,M,t,N,n,c]);return s.createElement(i.Provider,{value:E},s.createElement(u,{forcedTheme:t,storageKey:d,attribute:g,enableSystem:n,enableColorScheme:l,defaultTheme:h,value:v,themes:c,nonce:b,scriptProps:y}),j)},u=s.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:o,enableColorScheme:i,defaultTheme:l,value:d,themes:c,nonce:m,scriptProps:h}=e,u=JSON.stringify([a,r,l,t,c,d,o,i]).slice(1,-1);return s.createElement("script",{...h,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(n.toString(),")(").concat(u,")")}})}),x=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},p=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},f=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},2098:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2643:(e,t,r)=>{"use strict";r.d(t,{U:()=>n});var s=r(3865);function n(){return(0,s.createBrowserClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxb2l4dmdtcm9lamdhZWJ4eWVxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNjU0ODksImV4cCI6MjA2Njc0MTQ4OX0.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU")}},3509:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},6688:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>l});var s=r(5155),n=r(2115),a=r(2098),o=r(3509),i=r(1362);function l(){let{theme:e,setTheme:t}=(0,i.D)(),[r,l]=n.useState(!1);return(n.useEffect(()=>{l(!0)},[]),r)?(0,s.jsxs)("button",{onClick:()=>t("light"===e?"dark":"light"),className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:["light"===e?(0,s.jsx)(o.A,{className:"h-[1.2rem] w-[1.2rem]"}):(0,s.jsx)(a.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]}):(0,s.jsxs)("button",{className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:[(0,s.jsx)(a.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]})}},7339:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var s=r(5155),n=r(2115),a=r(5695),o=r(6874),i=r.n(o),l=r(2643),d=r(9946);let c=(0,d.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),m=(0,d.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),h=(0,d.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),u=(0,d.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function x(e){let{posts:t}=e,[r,o]=(0,n.useState)(t),[d,x]=(0,n.useState)(null),[p,f]=(0,n.useState)("latest"),[g,v]=(0,n.useState)(!1),j=(0,a.useRouter)(),b=async(e,t)=>{if(confirm('Are you sure you want to delete "'.concat(t,'"? This action cannot be undone.'))){x(e);try{let t=(0,l.U)(),{error:s}=await t.from("posts").delete().eq("id",e);if(s)throw s;o(r.filter(t=>t.id!==e)),j.refresh()}catch(e){console.error("Error deleting post:",e),alert("Error deleting post. Please try again.")}finally{x(null)}}},y=async()=>{v(!0);try{let e=(0,l.U)(),{data:t,error:r}=await e.from("posts").select("*").order("created_at",{ascending:!1});if(r)throw r;o(t||[]),j.refresh()}catch(e){console.error("Error refreshing posts:",e),alert("Error refreshing posts. Please try again.")}finally{v(!1)}},k=[...r].sort((e,t)=>{switch(p){case"latest":return new Date(t.created_at).getTime()-new Date(e.created_at).getTime();case"oldest":return new Date(e.created_at).getTime()-new Date(t.created_at).getTime();case"title-asc":return e.title.localeCompare(t.title);case"title-desc":return t.title.localeCompare(e.title);default:return 0}});return 0===r.length?(0,s.jsx)("div",{className:"text-center py-16 lg:py-24",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-muted-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsx)("h2",{className:"text-2xl font-semibold text-foreground mb-2",children:"No posts yet"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-6",children:"Start creating content for your blog!"}),(0,s.jsxs)(i(),{href:"/admin/new-post",className:"inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Create your first post"]})]})}):(0,s.jsxs)("div",{className:"space-y-6 lg:space-y-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 lg:gap-6 mb-8",children:[(0,s.jsx)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift animate-fade-in shadow-sm hover:shadow-md transition-all duration-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-primary/10 rounded-xl",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Posts"}),(0,s.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-foreground",children:r.length})]})]})}),(0,s.jsx)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift animate-fade-in shadow-sm hover:shadow-md transition-all duration-200",style:{animationDelay:"0.1s"},children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/20 rounded-xl",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Published Today"}),(0,s.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-foreground",children:r.filter(e=>{let t=new Date;return new Date(e.created_at).toDateString()===t.toDateString()}).length})]})]})}),(0,s.jsx)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift animate-fade-in shadow-sm hover:shadow-md transition-all duration-200",style:{animationDelay:"0.2s"},children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"This Week"}),(0,s.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-foreground",children:r.filter(e=>{let t=new Date;return t.setDate(t.getDate()-7),new Date(e.created_at)>t}).length})]})]})})]}),(0,s.jsxs)("div",{className:"bg-card rounded-2xl border border-border overflow-hidden shadow-xl",children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-muted/50 to-muted/30 px-6 py-4 border-b border-border",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"All Posts (",r.length,")"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"Sort by:"}),(0,s.jsxs)("select",{value:p,onChange:e=>f(e.target.value),className:"bg-background border border-input rounded-lg px-3 py-1 text-sm hover:border-primary/50 focus:border-primary focus:outline-none transition-colors",children:[(0,s.jsx)("option",{value:"latest",children:"Latest"}),(0,s.jsx)("option",{value:"oldest",children:"Oldest"}),(0,s.jsx)("option",{value:"title-asc",children:"Title A-Z"}),(0,s.jsx)("option",{value:"title-desc",children:"Title Z-A"})]})]})]})}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-muted/30",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-3 py-4 text-center text-xs font-semibold text-muted-foreground uppercase tracking-wider w-12",children:(0,s.jsx)("input",{type:"checkbox",className:"rounded border-border text-primary focus:ring-primary",onChange:e=>{document.querySelectorAll("input[data-post-id]").forEach(t=>{t.checked=e.target.checked})}})}),(0,s.jsx)("th",{className:"px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a.997.997 0 01-1.414 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z"})}),"Title"]})}),(0,s.jsx)("th",{className:"px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider hidden sm:table-cell",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),"Published"]})}),(0,s.jsx)("th",{className:"px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider hidden lg:table-cell",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Updated"]})}),(0,s.jsx)("th",{className:"px-6 py-4 text-center text-xs font-semibold text-muted-foreground uppercase tracking-wider",children:(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})}),"Actions"]})})]})}),(0,s.jsx)("tbody",{className:"divide-y divide-border/50",children:k.map((e,t)=>(0,s.jsxs)("tr",{className:"hover:bg-gradient-to-r hover:from-muted/20 hover:to-transparent transition-all duration-300 animate-slide-in group",style:{animationDelay:"".concat(.05*t,"s")},children:[(0,s.jsx)("td",{className:"px-3 py-6 text-center",children:(0,s.jsx)("input",{type:"checkbox","data-post-id":e.id,className:"rounded border-border text-primary focus:ring-primary"})}),(0,s.jsx)("td",{className:"px-6 py-6",children:(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-primary/20 transition-colors",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsx)("h4",{className:"text-base font-semibold text-card-foreground truncate group-hover:text-primary transition-colors",children:e.title}),(0,s.jsxs)("span",{className:"text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full",children:[Math.ceil(e.content.split(" ").length/200)," min"]})]}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground line-clamp-2 leading-relaxed",children:[e.content.substring(0,120),e.content.length>120&&"..."]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 mt-3 text-xs text-muted-foreground",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})}),e.content.split(" ").length," words"]}),(0,s.jsxs)("span",{className:"sm:hidden flex items-center",children:[(0,s.jsx)(c,{className:"w-3 h-3 mr-1"}),new Date(e.created_at).toLocaleDateString("en-US",{month:"short",day:"numeric"})]})]})]})]})}),(0,s.jsx)("td",{className:"px-6 py-6 whitespace-nowrap hidden sm:table-cell",children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center text-sm font-medium text-foreground mb-1",children:[(0,s.jsx)(c,{className:"w-4 h-4 mr-2 text-primary"}),new Date(e.created_at).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})]}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:new Date(e.created_at).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})})]})}),(0,s.jsx)("td",{className:"px-6 py-6 whitespace-nowrap hidden lg:table-cell",children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-foreground mb-1",children:new Date(e.updated_at).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.updated_at!==e.created_at?(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Modified"]}):"No changes"})]})}),(0,s.jsx)("td",{className:"px-6 py-6 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(i(),{href:"/posts/".concat(e.id),className:"inline-flex items-center justify-center w-9 h-9 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-200 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 group/action",title:"View post",children:(0,s.jsx)(m,{className:"w-4 h-4 group-hover/action:scale-110 transition-transform"})}),(0,s.jsx)(i(),{href:"/admin/edit-post/".concat(e.id),className:"inline-flex items-center justify-center w-9 h-9 text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 transition-all duration-200 rounded-xl hover:bg-green-50 dark:hover:bg-green-900/20 group/action",title:"Edit post",children:(0,s.jsx)(h,{className:"w-4 h-4 group-hover/action:scale-110 transition-transform"})}),(0,s.jsx)("button",{onClick:()=>b(e.id,e.title),disabled:d===e.id,className:"inline-flex items-center justify-center w-9 h-9 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-all duration-200 rounded-xl hover:bg-red-50 dark:hover:bg-red-900/20 disabled:opacity-50 disabled:cursor-not-allowed group/action",title:"Delete post",children:d===e.id?(0,s.jsx)("div",{className:"w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent"}):(0,s.jsx)(u,{className:"w-4 h-4 group-hover/action:scale-110 transition-transform"})})]})})]},e.id))})]})}),(0,s.jsx)("div",{className:"bg-muted/20 px-6 py-4 border-t border-border",children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),"Showing ",r.length," ",1===r.length?"post":"posts"]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("button",{onClick:()=>{let e="data:application/json;charset=utf-8,"+encodeURIComponent(JSON.stringify(r,null,2)),t="blog-posts-".concat(new Date().toISOString().split("T")[0],".json"),s=document.createElement("a");s.setAttribute("href",e),s.setAttribute("download",t),s.click()},className:"inline-flex items-center px-3 py-2 text-sm font-medium text-muted-foreground bg-background border border-border rounded-lg hover:bg-muted hover:text-foreground transition-colors",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"})}),"Export"]}),(0,s.jsxs)("button",{onClick:y,disabled:g,className:"inline-flex items-center px-3 py-2 text-sm font-medium text-muted-foreground bg-background border border-border rounded-lg hover:bg-muted hover:text-foreground transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2 ".concat(g?"animate-spin":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),g?"Refreshing...":"Refresh"]})]})]})})]}),(0,s.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h4",{className:"font-semibold text-foreground",children:"Quick Actions"}),(0,s.jsx)("svg",{className:"w-5 h-5 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(i(),{href:"/admin/new-post",className:"flex items-center text-sm text-muted-foreground hover:text-primary transition-colors",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Create new post"]}),(0,s.jsxs)("button",{onClick:()=>{let e=document.createElement("input");e.type="file",e.accept=".json",e.onchange=async e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r)try{let e=await r.text(),t=JSON.parse(e);if(!Array.isArray(t))return void alert("Invalid file format. Please select a valid JSON file with posts array.");let s=(0,l.U)(),{error:n}=await s.from("posts").insert(t.map(e=>({title:e.title,content:e.content,created_at:e.created_at||new Date().toISOString(),updated_at:new Date().toISOString()})));if(n)throw n;alert("Successfully imported ".concat(t.length," posts!")),y()}catch(e){console.error("Error importing posts:",e),alert("Error importing posts. Please check the file format and try again.")}},e.click()},className:"flex items-center text-sm text-muted-foreground hover:text-primary transition-colors",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"})}),"Import posts"]}),(0,s.jsxs)("button",{onClick:()=>{let e=r.filter((e,t)=>{let s=document.querySelector('input[data-post-id="'.concat(r[t].id,'"]'));return null==s?void 0:s.checked});if(0===e.length)return void alert("Please select posts to perform bulk actions.");let t=prompt("Selected ".concat(e.length," posts. Choose action:\n1. Delete\n2. Export\n\nEnter 1 or 2:"));if("1"===t)confirm("Are you sure you want to delete ".concat(e.length," selected posts? This action cannot be undone."))&&e.forEach(e=>b(e.id,e.title));else if("2"===t){let t="data:application/json;charset=utf-8,"+encodeURIComponent(JSON.stringify(e,null,2)),r="selected-posts-".concat(new Date().toISOString().split("T")[0],".json"),s=document.createElement("a");s.setAttribute("href",t),s.setAttribute("download",r),s.click()}},className:"flex items-center text-sm text-muted-foreground hover:text-primary transition-colors",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"})}),"Bulk actions"]})]})]}),(0,s.jsxs)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h4",{className:"font-semibold text-foreground",children:"Recent Activity"}),(0,s.jsx)("svg",{className:"w-5 h-5 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})]}),(0,s.jsx)("div",{className:"space-y-3",children:r.slice(0,3).map(e=>(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),(0,s.jsxs)("span",{className:"text-muted-foreground truncate",children:[e.title.substring(0,30),"..."]})]},e.id))})]}),(0,s.jsxs)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h4",{className:"font-semibold text-foreground",children:"Storage"}),(0,s.jsx)("svg",{className:"w-5 h-5 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Posts"}),(0,s.jsx)("span",{className:"font-medium",children:r.length})]}),(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Total words"}),(0,s.jsx)("span",{className:"font-medium",children:r.reduce((e,t)=>e+t.content.split(" ").length,0).toLocaleString()})]}),(0,s.jsx)("div",{className:"w-full bg-muted rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-primary h-2 rounded-full w-3/4"})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"75% of storage used"})]})]})]})]})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(2115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:c="",children:m,iconNode:h,...u}=e;return(0,s.createElement)("svg",{ref:t,...d,width:n,height:n,stroke:r,strokeWidth:o?24*Number(a)/Number(n):a,className:i("lucide",c),...!m&&!l(u)&&{"aria-hidden":"true"},...u},[...h.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let r=(0,s.forwardRef)((r,a)=>{let{className:l,...d}=r;return(0,s.createElement)(c,{ref:a,iconNode:t,className:i("lucide-".concat(n(o(e))),"lucide-".concat(e),l),...d})});return r.displayName=o(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,271,441,684,358],()=>t(419)),_N_E=e.O()}]);