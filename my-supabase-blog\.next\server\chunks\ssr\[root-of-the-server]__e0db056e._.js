module.exports = {

"[project]/src/components/markdown-editor/utils/text-api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CommandOrchestrator": (()=>CommandOrchestrator),
    "TextAreaAPIImpl": (()=>TextAreaAPIImpl),
    "getStateFromTextArea": (()=>getStateFromTextArea),
    "getWordCount": (()=>getWordCount),
    "insertAtLineStart": (()=>insertAtLineStart),
    "insertBlock": (()=>insertBlock),
    "insertTextAtPosition": (()=>insertTextAtPosition),
    "toggleLinePrefix": (()=>toggleLinePrefix),
    "wrapText": (()=>wrapText)
});
function insertTextAtPosition(textarea, text, position) {
    const pos = position ?? textarea.selectionStart;
    const before = textarea.value.substring(0, pos);
    const after = textarea.value.substring(pos);
    textarea.value = before + text + after;
    textarea.selectionStart = textarea.selectionEnd = pos + text.length;
    // Trigger change event
    const event = new Event('input', {
        bubbles: true
    });
    textarea.dispatchEvent(event);
}
function getStateFromTextArea(textarea) {
    return {
        selection: {
            start: textarea.selectionStart,
            end: textarea.selectionEnd
        },
        text: textarea.value,
        selectedText: textarea.value.slice(textarea.selectionStart, textarea.selectionEnd)
    };
}
class TextAreaAPIImpl {
    textarea;
    constructor(textarea){
        this.textarea = textarea;
    }
    /**
   * Replace the current selection with new text
   */ replaceSelection(text) {
        const start = this.textarea.selectionStart;
        const end = this.textarea.selectionEnd;
        const before = this.textarea.value.substring(0, start);
        const after = this.textarea.value.substring(end);
        this.textarea.value = before + text + after;
        this.textarea.selectionStart = this.textarea.selectionEnd = start + text.length;
        // Focus and trigger change event
        this.textarea.focus();
        const event = new Event('input', {
            bubbles: true
        });
        this.textarea.dispatchEvent(event);
        return this.getState();
    }
    /**
   * Set selection range
   */ setSelectionRange(selection) {
        this.textarea.focus();
        this.textarea.selectionStart = selection.start;
        this.textarea.selectionEnd = selection.end;
        return this.getState();
    }
    /**
   * Insert text at specific position
   */ insertText(text, position) {
        insertTextAtPosition(this.textarea, text, position);
        return this.getState();
    }
    /**
   * Get current state
   */ getState() {
        return getStateFromTextArea(this.textarea);
    }
}
class CommandOrchestrator {
    textarea;
    textApi;
    constructor(textarea){
        this.textarea = textarea;
        this.textApi = new TextAreaAPIImpl(textarea);
    }
    getTextApi() {
        return this.textApi;
    }
    getState() {
        if (!this.textarea) return false;
        return getStateFromTextArea(this.textarea);
    }
}
function wrapText(state, api, prefix, suffix = prefix) {
    const { selectedText } = state;
    const newText = `${prefix}${selectedText}${suffix}`;
    api.replaceSelection(newText);
}
function insertAtLineStart(state, api, prefix) {
    const { selectedText } = state;
    const lines = selectedText.split('\n');
    const newText = lines.map((line)=>`${prefix}${line}`).join('\n');
    api.replaceSelection(newText);
}
function toggleLinePrefix(state, api, prefix) {
    const { selectedText } = state;
    const lines = selectedText.split('\n');
    // Check if all lines start with prefix
    const allHavePrefix = lines.every((line)=>line.startsWith(prefix));
    let newText;
    if (allHavePrefix) {
        // Remove prefix from all lines
        newText = lines.map((line)=>line.startsWith(prefix) ? line.slice(prefix.length) : line).join('\n');
    } else {
        // Add prefix to all lines
        newText = lines.map((line)=>`${prefix}${line}`).join('\n');
    }
    api.replaceSelection(newText);
}
function insertBlock(state, api, text) {
    const { selection } = state;
    const beforeText = state.text.substring(0, selection.start);
    const afterText = state.text.substring(selection.end);
    // Add line breaks if needed
    let prefix = '';
    let suffix = '';
    if (beforeText && !beforeText.endsWith('\n')) {
        prefix = '\n';
    }
    if (afterText && !afterText.startsWith('\n')) {
        suffix = '\n';
    }
    api.replaceSelection(`${prefix}${text}${suffix}`);
}
function getWordCount(text) {
    const words = text.trim().split(/\s+/).filter((word)=>word.length > 0).length;
    const characters = text.length;
    const charactersNoSpaces = text.replace(/\s/g, '').length;
    const lines = text.split('\n').length;
    return {
        words,
        characters,
        charactersNoSpaces,
        lines
    };
}
}}),
"[project]/src/components/markdown-editor/commands/index.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "bold": (()=>bold),
    "checkedList": (()=>checkedList),
    "code": (()=>code),
    "codeBlock": (()=>codeBlock),
    "divider": (()=>divider),
    "editMode": (()=>editMode),
    "fullscreen": (()=>fullscreen),
    "getDefaultCommands": (()=>getDefaultCommands),
    "getDefaultExtraCommands": (()=>getDefaultExtraCommands),
    "h1": (()=>h1),
    "h2": (()=>h2),
    "h3": (()=>h3),
    "headingGroup": (()=>headingGroup),
    "hr": (()=>hr),
    "image": (()=>image),
    "italic": (()=>italic),
    "link": (()=>link),
    "liveMode": (()=>liveMode),
    "orderedList": (()=>orderedList),
    "previewMode": (()=>previewMode),
    "quote": (()=>quote),
    "strikethrough": (()=>strikethrough),
    "unorderedList": (()=>unorderedList)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/utils/text-api.ts [app-ssr] (ecmascript)");
;
;
const h1 = {
    name: 'h1',
    keyCommand: 'h1',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M4 12h8m-8-6v12m8-12v12M17 7v10M21 7v10"
        }, void 0, false, {
            fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Heading 1',
        title: 'Heading 1'
    },
    execute: (state, api)=>{
        const text = state.selectedText || 'Heading 1';
        api.replaceSelection(`# ${text}`);
    }
};
const h2 = {
    name: 'h2',
    keyCommand: 'h2',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M4 12h8m-8-6v12m8-12v12M17 7h4l-4 5h4v3h-4"
        }, void 0, false, {
            fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
            lineNumber: 25,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Heading 2',
        title: 'Heading 2'
    },
    execute: (state, api)=>{
        const text = state.selectedText || 'Heading 2';
        api.replaceSelection(`## ${text}`);
    }
};
const h3 = {
    name: 'h3',
    keyCommand: 'h3',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M4 12h8m-8-6v12m8-12v12M17 7h4l-2 3 2 3h-4"
        }, void 0, false, {
            fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
            lineNumber: 40,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Heading 3',
        title: 'Heading 3'
    },
    execute: (state, api)=>{
        const text = state.selectedText || 'Heading 3';
        api.replaceSelection(`### ${text}`);
    }
};
const headingGroup = {
    name: 'heading',
    keyCommand: 'heading',
    groupName: 'heading',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M6 12h12M6 20V4M18 20V4"
        }, void 0, false, {
            fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
            lineNumber: 57,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 56,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Headings',
        title: 'Headings'
    },
    children: [
        h1,
        h2,
        h3
    ]
};
const bold = {
    name: 'bold',
    keyCommand: 'bold',
    shortcuts: [
        'ctrl+b',
        'cmd+b'
    ],
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 71,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 70,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Bold',
        title: 'Bold (Ctrl+B)'
    },
    execute: (state, api)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrapText"])(state, api, '**');
    }
};
const italic = {
    name: 'italic',
    keyCommand: 'italic',
    shortcuts: [
        'ctrl+i',
        'cmd+i'
    ],
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M19 4h-9M14 20H5M15 4L9 20"
        }, void 0, false, {
            fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
            lineNumber: 87,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 86,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Italic',
        title: 'Italic (Ctrl+I)'
    },
    execute: (state, api)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrapText"])(state, api, '*');
    }
};
const strikethrough = {
    name: 'strikethrough',
    keyCommand: 'strikethrough',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M16 4H9a3 3 0 0 0-2.83 4M14 12a4 4 0 0 1 0 8H6"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 101,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "4",
                y1: "12",
                x2: "20",
                y2: "12"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 102,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 100,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Strikethrough',
        title: 'Strikethrough'
    },
    execute: (state, api)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrapText"])(state, api, '~~');
    }
};
const code = {
    name: 'code',
    keyCommand: 'code',
    shortcuts: [
        'ctrl+`',
        'cmd+`'
    ],
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("polyline", {
                points: "16,18 22,12 16,6"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 117,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("polyline", {
                points: "8,6 2,12 8,18"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 118,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 116,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Inline code',
        title: 'Inline code (Ctrl+`)'
    },
    execute: (state, api)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wrapText"])(state, api, '`');
    }
};
const codeBlock = {
    name: 'codeBlock',
    keyCommand: 'codeBlock',
    shortcuts: [
        'ctrl+shift+c',
        'cmd+shift+c'
    ],
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                x: "3",
                y: "3",
                width: "18",
                height: "18",
                rx: "2",
                ry: "2"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 133,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("polyline", {
                points: "8,12 12,16 16,12"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 134,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 132,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Code block',
        title: 'Code block (Ctrl+Shift+C)'
    },
    execute: (state, api)=>{
        const codeBlockText = state.selectedText || 'code';
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["insertBlock"])(state, api, `\`\`\`\n${codeBlockText}\n\`\`\``);
    }
};
const link = {
    name: 'link',
    keyCommand: 'link',
    shortcuts: [
        'ctrl+k',
        'cmd+k'
    ],
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 151,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 152,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 150,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Link',
        title: 'Link (Ctrl+K)'
    },
    execute: (state, api)=>{
        const linkText = state.selectedText || 'link text';
        api.replaceSelection(`[${linkText}](url)`);
    }
};
const image = {
    name: 'image',
    keyCommand: 'image',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                x: "3",
                y: "3",
                width: "18",
                height: "18",
                rx: "2",
                ry: "2"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 167,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "8.5",
                cy: "8.5",
                r: "1.5"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 168,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("polyline", {
                points: "21,15 16,10 5,21"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 169,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 166,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Image',
        title: 'Image'
    },
    execute: (state, api)=>{
        const altText = state.selectedText || 'alt text';
        api.replaceSelection(`![${altText}](image-url)`);
    }
};
const unorderedList = {
    name: 'unorderedList',
    keyCommand: 'unorderedList',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "8",
                y1: "6",
                x2: "21",
                y2: "6"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 185,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "8",
                y1: "12",
                x2: "21",
                y2: "12"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 186,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "8",
                y1: "18",
                x2: "21",
                y2: "18"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 187,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "3",
                y1: "6",
                x2: "3.01",
                y2: "6"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 188,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "3",
                y1: "12",
                x2: "3.01",
                y2: "12"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 189,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "3",
                y1: "18",
                x2: "3.01",
                y2: "18"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 190,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 184,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Unordered list',
        title: 'Unordered list'
    },
    execute: (state, api)=>{
        if (state.selectedText) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toggleLinePrefix"])(state, api, '- ');
        } else {
            api.replaceSelection('- ');
        }
    }
};
const orderedList = {
    name: 'orderedList',
    keyCommand: 'orderedList',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "10",
                y1: "6",
                x2: "21",
                y2: "6"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 208,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "10",
                y1: "12",
                x2: "21",
                y2: "12"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 209,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "10",
                y1: "18",
                x2: "21",
                y2: "18"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 210,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M4 6h1v4"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 211,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M4 10h2"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 212,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 213,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 207,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Ordered list',
        title: 'Ordered list'
    },
    execute: (state, api)=>{
        if (state.selectedText) {
            const lines = state.selectedText.split('\n');
            const newText = lines.map((line, index)=>`${index + 1}. ${line}`).join('\n');
            api.replaceSelection(newText);
        } else {
            api.replaceSelection('1. ');
        }
    }
};
const checkedList = {
    name: 'checkedList',
    keyCommand: 'checkedList',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("polyline", {
                points: "9,11 12,14 22,4"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 233,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M21,12v7a2,2 0,0 1,-2,2H5a2,2 0,0 1,-2,-2V5a2,2 0,0 1,2,-2h11"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 234,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 232,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Task list',
        title: 'Task list'
    },
    execute: (state, api)=>{
        if (state.selectedText) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toggleLinePrefix"])(state, api, '- [ ] ');
        } else {
            api.replaceSelection('- [ ] ');
        }
    }
};
const quote = {
    name: 'quote',
    keyCommand: 'quote',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 253,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 254,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 252,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Quote',
        title: 'Quote'
    },
    execute: (state, api)=>{
        if (state.selectedText) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toggleLinePrefix"])(state, api, '> ');
        } else {
            api.replaceSelection('> ');
        }
    }
};
const hr = {
    name: 'hr',
    keyCommand: 'hr',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
            x1: "3",
            y1: "12",
            x2: "21",
            y2: "12"
        }, void 0, false, {
            fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
            lineNumber: 272,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 271,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Horizontal rule',
        title: 'Horizontal rule'
    },
    execute: (state, api)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["insertBlock"])(state, api, '---');
    }
};
const divider = {
    name: 'divider',
    keyCommand: 'divider',
    render: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-px h-6 bg-border mx-1",
            role: "separator",
            "aria-orientation": "vertical"
        }, void 0, false, {
            fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
            lineNumber: 286,
            columnNumber: 5
        }, this)
};
const editMode = {
    name: 'edit',
    keyCommand: 'edit',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 296,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 297,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 295,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Edit mode',
        title: 'Edit mode'
    },
    execute: (_state, _api, dispatch)=>{
        dispatch?.({
            preview: 'edit'
        });
    }
};
const previewMode = {
    name: 'preview',
    keyCommand: 'preview',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 311,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                cx: "12",
                cy: "12",
                r: "3"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 312,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 310,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Preview mode',
        title: 'Preview mode'
    },
    execute: (_state, _api, dispatch)=>{
        dispatch?.({
            preview: 'preview'
        });
    }
};
const liveMode = {
    name: 'live',
    keyCommand: 'live',
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                x: "3",
                y: "3",
                width: "18",
                height: "18",
                rx: "2",
                ry: "2"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 326,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "9",
                y1: "9",
                x2: "15",
                y2: "15"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 327,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                x1: "15",
                y1: "9",
                x2: "9",
                y2: "15"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
                lineNumber: 328,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 325,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Live mode',
        title: 'Live mode'
    },
    execute: (_state, _api, dispatch)=>{
        dispatch?.({
            preview: 'live'
        });
    }
};
const fullscreen = {
    name: 'fullscreen',
    keyCommand: 'fullscreen',
    shortcuts: [
        'F11'
    ],
    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        width: "12",
        height: "12",
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        strokeWidth: 2,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"
        }, void 0, false, {
            fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
            lineNumber: 343,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/markdown-editor/commands/index.tsx",
        lineNumber: 342,
        columnNumber: 5
    }, this),
    buttonProps: {
        'aria-label': 'Fullscreen',
        title: 'Fullscreen (F11)'
    },
    execute: (_state, _api, dispatch, executeState)=>{
        dispatch?.({
            fullscreen: !executeState?.fullscreen
        });
    }
};
function getDefaultCommands() {
    return [
        bold,
        italic,
        strikethrough,
        divider,
        headingGroup,
        divider,
        code,
        codeBlock,
        divider,
        link,
        image,
        divider,
        unorderedList,
        orderedList,
        checkedList,
        divider,
        quote,
        hr
    ];
}
function getDefaultExtraCommands() {
    return [
        editMode,
        liveMode,
        previewMode,
        divider,
        fullscreen
    ];
}
}}),
"[project]/src/components/markdown-editor/context.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EditorContext": (()=>EditorContext),
    "EditorProvider": (()=>EditorProvider),
    "closeAllPopups": (()=>closeAllPopups),
    "setGroupPopupStates": (()=>setGroupPopupStates),
    "togglePopup": (()=>togglePopup),
    "useEditor": (()=>useEditor),
    "useEditorDispatch": (()=>useEditorDispatch),
    "useEditorState": (()=>useEditorState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$commands$2f$index$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/commands/index.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
// Default editor state
const defaultState = {
    markdown: '',
    preview: 'live',
    fullscreen: false,
    height: 400,
    highlightEnable: true,
    tabSize: 2,
    defaultTabEnable: false,
    scrollTop: 0,
    scrollTopPreview: 0,
    commands: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$commands$2f$index$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDefaultCommands"])(),
    extraCommands: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$commands$2f$index$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDefaultExtraCommands"])(),
    barPopup: {}
};
// Reducer function
function editorReducer(state, action) {
    return {
        ...state,
        ...action
    };
}
// Create context
const EditorContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function EditorProvider({ children, initialState = {} }) {
    const [state, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useReducer"])(editorReducer, {
        ...defaultState,
        ...initialState
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(EditorContext.Provider, {
        value: {
            state,
            dispatch
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/markdown-editor/context.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
function useEditor() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(EditorContext);
    if (context === undefined) {
        throw new Error('useEditor must be used within an EditorProvider');
    }
    return context;
}
function useEditorState() {
    const { state } = useEditor();
    return state;
}
function useEditorDispatch() {
    const { dispatch } = useEditor();
    return dispatch;
}
function setGroupPopupStates(popupStates, value = false) {
    const newStates = {};
    Object.keys(popupStates).forEach((key)=>{
        newStates[key] = value;
    });
    return newStates;
}
function togglePopup(popupStates, key) {
    return {
        ...setGroupPopupStates(popupStates, false),
        [key]: !popupStates[key]
    };
}
function closeAllPopups(popupStates) {
    return setGroupPopupStates(popupStates, false);
}
;
}}),
"[project]/src/components/markdown-editor/components/textarea.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/context.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/utils/text-api.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
// Syntax highlighting for markdown
function highlightMarkdown(text) {
    return text// Headers
    .replace(/^(#{1,6})\s+(.*)$/gm, '<span class="text-blue-600 dark:text-blue-400 font-bold">$1</span> <span class="text-gray-900 dark:text-gray-100 font-semibold">$2</span>')// Bold
    .replace(/\*\*(.*?)\*\*/g, '<span class="font-bold text-gray-900 dark:text-gray-100">**$1**</span>')// Italic
    .replace(/\*(.*?)\*/g, '<span class="italic text-gray-700 dark:text-gray-300">*$1*</span>')// Inline code
    .replace(/`([^`]+)`/g, '<span class="bg-gray-100 dark:bg-gray-800 text-red-600 dark:text-red-400 px-1 rounded font-mono text-sm">`$1`</span>')// Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<span class="text-blue-600 dark:text-blue-400 underline">[$1]($2)</span>')// Code blocks
    .replace(/^```[\s\S]*?```$/gm, '<span class="bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 block p-2 rounded font-mono text-sm">$&</span>')// Lists
    .replace(/^(\s*[-*+])\s+(.*)$/gm, '<span class="text-purple-600 dark:text-purple-400">$1</span> <span class="text-gray-900 dark:text-gray-100">$2</span>').replace(/^(\s*\d+\.)\s+(.*)$/gm, '<span class="text-purple-600 dark:text-purple-400">$1</span> <span class="text-gray-900 dark:text-gray-100">$2</span>')// Quotes
    .replace(/^>\s+(.*)$/gm, '<span class="text-gray-600 dark:text-gray-400 border-l-4 border-gray-300 dark:border-gray-600 pl-2 italic">> $1</span>')// Horizontal rules
    .replace(/^---+$/gm, '<span class="text-gray-400 dark:text-gray-600">$&</span>');
}
const Textarea = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ value = '', onChange, onScroll, highlightEnable = true, tabSize = 2, defaultTabEnable = false, renderTextarea, className = '', style, ...props }, ref)=>{
    const { state, dispatch } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEditor"])();
    const textareaRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const preRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const wrapRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const orchestratorRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Expose methods via ref
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, ()=>({
            focus: ()=>textareaRef.current?.focus(),
            blur: ()=>textareaRef.current?.blur(),
            getTextArea: ()=>textareaRef.current
        }));
    // Initialize command orchestrator
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (textareaRef.current) {
            orchestratorRef.current = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CommandOrchestrator"](textareaRef.current);
            dispatch({
                textarea: textareaRef.current,
                textareaWrap: wrapRef.current || undefined,
                textareaPre: preRef.current || undefined
            });
        }
    }, [
        dispatch
    ]);
    // Handle tab key behavior
    const handleKeyDown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        if (event.key === 'Tab' && !defaultTabEnable) {
            event.preventDefault();
            const textarea = event.currentTarget;
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const spaces = ' '.repeat(tabSize);
            // Insert spaces
            const newValue = value.substring(0, start) + spaces + value.substring(end);
            onChange?.({
                ...event,
                currentTarget: {
                    ...textarea,
                    value: newValue
                }
            });
            // Set cursor position
            setTimeout(()=>{
                textarea.selectionStart = textarea.selectionEnd = start + spaces.length;
            }, 0);
        }
        // Handle other keyboard shortcuts
        if (event.ctrlKey || event.metaKey) {
            const key = event.key.toLowerCase();
            const shortcuts = state.commands.concat(state.extraCommands).filter((cmd)=>cmd.shortcuts?.some((shortcut)=>shortcut.toLowerCase().includes(key) && (shortcut.includes('ctrl') && event.ctrlKey || shortcut.includes('cmd') && event.metaKey)));
            if (shortcuts.length > 0) {
                event.preventDefault();
                const command = shortcuts[0];
                if (command.execute && orchestratorRef.current) {
                    const textApi = orchestratorRef.current.getTextApi();
                    const textState = orchestratorRef.current.getState();
                    if (textState) {
                        command.execute(textState, textApi, dispatch);
                    }
                }
            }
        }
    }, [
        defaultTabEnable,
        tabSize,
        value,
        onChange,
        state.commands,
        state.extraCommands,
        dispatch
    ]);
    // Sync scroll between textarea and pre
    const handleTextareaScroll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        if (preRef.current && highlightEnable) {
            preRef.current.scrollTop = event.currentTarget.scrollTop;
            preRef.current.scrollLeft = event.currentTarget.scrollLeft;
        }
        onScroll?.(event);
    }, [
        highlightEnable,
        onScroll
    ]);
    // Update syntax highlighting
    const highlightedContent = highlightEnable ? highlightMarkdown(value) : '';
    // Custom textarea renderer
    if (renderTextarea) {
        return renderTextarea({
            value,
            onChange,
            onScroll: handleTextareaScroll,
            onKeyDown: handleKeyDown,
            highlightEnable,
            tabSize,
            defaultTabEnable,
            className,
            style,
            ...props
        });
    }
    // Safe window access for SSR compatibility
    const isMobile = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : false;
    const fontSize = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : '14px';
    const padding = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : '16px';
    // Check if we're in fullscreen mode - if so, ensure text is always visible
    const isFullscreen = state.fullscreen;
    const shouldShowText = !highlightEnable || isFullscreen;
    const textareaStyles = {
        resize: 'none',
        outline: 'none',
        border: 'none',
        background: 'transparent',
        // Fix for fullscreen text visibility: ensure text is always visible in fullscreen
        color: shouldShowText ? 'hsl(var(--foreground))' : 'transparent',
        caretColor: 'hsl(var(--foreground))',
        fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
        fontSize,
        lineHeight: '1.6',
        padding,
        width: '100%',
        height: '100%',
        position: highlightEnable && !isFullscreen ? 'absolute' : 'relative',
        top: 0,
        left: 0,
        zIndex: highlightEnable && !isFullscreen ? 2 : 1,
        // Ensure text is visible in fullscreen mode
        WebkitTextFillColor: shouldShowText ? 'hsl(var(--foreground))' : 'transparent',
        ...style
    };
    const preStyles = {
        margin: 0,
        padding,
        fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
        fontSize,
        lineHeight: '1.6',
        whiteSpace: 'pre-wrap',
        wordWrap: 'break-word',
        overflow: 'hidden',
        position: highlightEnable && !isFullscreen ? 'absolute' : 'relative',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 1,
        pointerEvents: 'none',
        background: 'transparent',
        color: 'hsl(var(--foreground))',
        // Hide syntax highlighting in fullscreen to prevent conflicts
        display: highlightEnable && !isFullscreen ? 'block' : 'none'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: wrapRef,
        className: `relative w-full h-full min-h-[300px] ${className}`,
        style: {
            position: 'relative',
            // Ensure proper stacking in fullscreen mode
            zIndex: isFullscreen ? 'auto' : 'initial'
        },
        children: [
            highlightEnable && !isFullscreen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                ref: preRef,
                style: preStyles,
                dangerouslySetInnerHTML: {
                    __html: highlightedContent
                },
                "aria-hidden": "true"
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/components/textarea.tsx",
                lineNumber: 210,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                ref: textareaRef,
                value: value,
                onChange: onChange,
                onScroll: handleTextareaScroll,
                onKeyDown: handleKeyDown,
                style: textareaStyles,
                spellCheck: false,
                autoComplete: "off",
                autoCorrect: "off",
                autoCapitalize: "off",
                "data-gramm": "false",
                ...props
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/components/textarea.tsx",
                lineNumber: 217,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/components/textarea.tsx",
        lineNumber: 200,
        columnNumber: 5
    }, this);
});
Textarea.displayName = 'Textarea';
;
}}),
"[project]/src/components/markdown-editor/components/toolbar.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toolbar": (()=>Toolbar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/context.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
function CommandButton({ command, disabled = false, executeCommand, index }) {
    const { state, dispatch } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEditor"])();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const dropdownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Handle click outside to close dropdown
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        function handleClickOutside(event) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        }
        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
            return ()=>document.removeEventListener('mousedown', handleClickOutside);
        }
    }, [
        isOpen
    ]);
    // Custom render function
    if (command.render) {
        return command.render(command, disabled, executeCommand, index) || null;
    }
    // Divider
    if (command.name === 'divider') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-px h-6 bg-border mx-1",
            role: "separator",
            "aria-orientation": "vertical"
        }, void 0, false, {
            fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
            lineNumber: 41,
            columnNumber: 7
        }, this);
    }
    // Group with children commands
    if (command.children && Array.isArray(command.children)) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative",
            ref: dropdownRef,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    type: "button",
                    disabled: disabled,
                    onClick: ()=>setIsOpen(!isOpen),
                    className: `
            inline-flex items-center justify-center
            w-8 h-8 sm:w-8 sm:h-8 min-w-[44px] min-h-[44px] sm:min-w-[32px] sm:min-h-[32px]
            rounded-md p-1 sm:p-0
            hover:bg-accent hover:text-accent-foreground
            focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-colors duration-200
            ${isOpen ? 'bg-accent text-accent-foreground' : 'text-muted-foreground'}
          `,
                    "aria-label": command.buttonProps?.['aria-label'] || command.name,
                    title: command.buttonProps?.title || command.name,
                    ...command.buttonProps,
                    children: [
                        command.icon,
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-3 h-3 ml-1",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M19 9l-7 7-7-7"
                            }, void 0, false, {
                                fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                                lineNumber: 78,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                            lineNumber: 72,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                    lineNumber: 53,
                    columnNumber: 9
                }, this),
                isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute top-full left-0 mt-1 bg-popover border border-border rounded-md shadow-lg z-50 min-w-[120px]",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "py-1",
                        children: command.children.map((childCommand, childIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                disabled: disabled,
                                onClick: ()=>{
                                    executeCommand(childCommand, command.groupName);
                                    setIsOpen(false);
                                },
                                className: "w-full px-3 py-2 text-left text-sm hover:bg-accent hover:text-accent-foreground flex items-center",
                                "aria-label": childCommand.buttonProps?.['aria-label'] || childCommand.name,
                                children: [
                                    childCommand.icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "mr-2 flex-shrink-0",
                                        children: childCommand.icon
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                                        lineNumber: 98,
                                        columnNumber: 21
                                    }, this),
                                    childCommand.name
                                ]
                            }, childCommand.name || childIndex, true, {
                                fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                                lineNumber: 86,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                        lineNumber: 84,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                    lineNumber: 83,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
            lineNumber: 52,
            columnNumber: 7
        }, this);
    }
    // Group with children function (popup content)
    if (command.children && typeof command.children === 'function') {
        const isPopupOpen = state.barPopup[command.groupName || command.name] || false;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative",
            ref: dropdownRef,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    type: "button",
                    disabled: disabled,
                    onClick: ()=>{
                        dispatch({
                            barPopup: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["togglePopup"])(state.barPopup, command.groupName || command.name)
                        });
                    },
                    className: `
            inline-flex items-center justify-center
            w-8 h-8 sm:w-8 sm:h-8 min-w-[44px] min-h-[44px] sm:min-w-[32px] sm:min-h-[32px]
            rounded-md p-1 sm:p-0
            hover:bg-accent hover:text-accent-foreground
            focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-colors duration-200
            ${isPopupOpen ? 'bg-accent text-accent-foreground' : 'text-muted-foreground'}
          `,
                    "aria-label": command.buttonProps?.['aria-label'] || command.name,
                    title: command.buttonProps?.title || command.name,
                    ...command.buttonProps,
                    children: command.icon
                }, void 0, false, {
                    fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                    lineNumber: 118,
                    columnNumber: 9
                }, this),
                isPopupOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute top-full left-0 mt-1 bg-popover border border-border rounded-md shadow-lg z-50",
                    children: command.children({
                        close: ()=>dispatch({
                                barPopup: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["closeAllPopups"])(state.barPopup)
                            }),
                        execute: ()=>executeCommand(command, command.groupName),
                        getState: ()=>state.textarea ? {
                                text: state.textarea.value,
                                selectedText: state.textarea.value.slice(state.textarea.selectionStart, state.textarea.selectionEnd),
                                selection: {
                                    start: state.textarea.selectionStart,
                                    end: state.textarea.selectionEnd
                                }
                            } : false,
                        textApi: state.textarea ? {
                            replaceSelection: (text)=>{
                                if (state.textarea) {
                                    const start = state.textarea.selectionStart;
                                    const end = state.textarea.selectionEnd;
                                    const before = state.textarea.value.substring(0, start);
                                    const after = state.textarea.value.substring(end);
                                    state.textarea.value = before + text + after;
                                    state.textarea.selectionStart = state.textarea.selectionEnd = start + text.length;
                                    state.textarea.focus();
                                    const event = new Event('input', {
                                        bubbles: true
                                    });
                                    state.textarea.dispatchEvent(event);
                                }
                                return state.textarea ? {
                                    text: state.textarea.value,
                                    selectedText: '',
                                    selection: {
                                        start: state.textarea.selectionStart,
                                        end: state.textarea.selectionEnd
                                    }
                                } : {
                                    text: '',
                                    selectedText: '',
                                    selection: {
                                        start: 0,
                                        end: 0
                                    }
                                };
                            },
                            setSelectionRange: (selection)=>{
                                if (state.textarea) {
                                    state.textarea.focus();
                                    state.textarea.selectionStart = selection.start;
                                    state.textarea.selectionEnd = selection.end;
                                }
                                return state.textarea ? {
                                    text: state.textarea.value,
                                    selectedText: state.textarea.value.slice(selection.start, selection.end),
                                    selection
                                } : {
                                    text: '',
                                    selectedText: '',
                                    selection: {
                                        start: 0,
                                        end: 0
                                    }
                                };
                            },
                            insertText: (text, position)=>{
                                if (state.textarea) {
                                    const pos = position ?? state.textarea.selectionStart;
                                    const before = state.textarea.value.substring(0, pos);
                                    const after = state.textarea.value.substring(pos);
                                    state.textarea.value = before + text + after;
                                    state.textarea.selectionStart = state.textarea.selectionEnd = pos + text.length;
                                    state.textarea.focus();
                                    const event = new Event('input', {
                                        bubbles: true
                                    });
                                    state.textarea.dispatchEvent(event);
                                }
                                return state.textarea ? {
                                    text: state.textarea.value,
                                    selectedText: '',
                                    selection: {
                                        start: state.textarea.selectionStart,
                                        end: state.textarea.selectionEnd
                                    }
                                } : {
                                    text: '',
                                    selectedText: '',
                                    selection: {
                                        start: 0,
                                        end: 0
                                    }
                                };
                            },
                            getState: ()=>state.textarea ? {
                                    text: state.textarea.value,
                                    selectedText: state.textarea.value.slice(state.textarea.selectionStart, state.textarea.selectionEnd),
                                    selection: {
                                        start: state.textarea.selectionStart,
                                        end: state.textarea.selectionEnd
                                    }
                                } : {
                                    text: '',
                                    selectedText: '',
                                    selection: {
                                        start: 0,
                                        end: 0
                                    }
                                }
                        } : {
                            replaceSelection: ()=>({
                                    text: '',
                                    selectedText: '',
                                    selection: {
                                        start: 0,
                                        end: 0
                                    }
                                }),
                            setSelectionRange: ()=>({
                                    text: '',
                                    selectedText: '',
                                    selection: {
                                        start: 0,
                                        end: 0
                                    }
                                }),
                            insertText: ()=>({
                                    text: '',
                                    selectedText: '',
                                    selection: {
                                        start: 0,
                                        end: 0
                                    }
                                }),
                            getState: ()=>({
                                    text: '',
                                    selectedText: '',
                                    selection: {
                                        start: 0,
                                        end: 0
                                    }
                                })
                        }
                    })
                }, void 0, false, {
                    fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                    lineNumber: 144,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
            lineNumber: 117,
            columnNumber: 7
        }, this);
    }
    // Regular button
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        type: "button",
        disabled: disabled,
        onClick: ()=>executeCommand(command, command.groupName),
        className: `
        inline-flex items-center justify-center
        w-8 h-8 sm:w-8 sm:h-8 min-w-[44px] min-h-[44px] sm:min-w-[32px] sm:min-h-[32px]
        rounded-md p-1 sm:p-0
        hover:bg-accent hover:text-accent-foreground
        focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2
        disabled:opacity-50 disabled:cursor-not-allowed
        transition-colors duration-200
        text-muted-foreground
      `,
        "aria-label": command.buttonProps?.['aria-label'] || command.name,
        title: command.buttonProps?.title || command.name,
        ...command.buttonProps,
        children: command.icon
    }, void 0, false, {
        fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
        lineNumber: 241,
        columnNumber: 5
    }, this);
}
function Toolbar({ commands, extraCommands, executeCommand, className = '', toolbarBottom = false }) {
    const { state, dispatch } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEditor"])();
    const handleContainerClick = ()=>{
        dispatch({
            barPopup: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["closeAllPopups"])(state.barPopup)
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `
        flex items-center justify-between
        px-2 py-2 sm:px-3 sm:py-2
        bg-card border-border toolbar
        ${toolbarBottom ? 'border-t' : 'border-b'}
        ${className}
      `,
        onClick: handleContainerClick,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-0.5 sm:space-x-1 overflow-x-auto toolbar-commands",
                children: commands.map((command, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CommandButton, {
                        command: command,
                        executeCommand: executeCommand,
                        index: index
                    }, command.name || index, false, {
                        fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                        lineNumber: 291,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                lineNumber: 289,
                columnNumber: 7
            }, this),
            extraCommands.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-0.5 sm:space-x-1 ml-2 sm:ml-4 flex-shrink-0",
                children: extraCommands.map((command, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CommandButton, {
                        command: command,
                        executeCommand: executeCommand,
                        index: index
                    }, command.name || index, false, {
                        fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                        lineNumber: 304,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
                lineNumber: 302,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/components/toolbar.tsx",
        lineNumber: 278,
        columnNumber: 5
    }, this);
}
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[project]/src/components/markdown-editor/components/preview.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MarkdownPreview": (()=>MarkdownPreview)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$markdown$2d$it$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/markdown-it/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highlight$2e$js$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/highlight.js/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highlight$2e$js$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/highlight.js/es/index.js [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
;
// Configure markdown-it with plugins
const createMarkdownRenderer = ()=>{
    const md = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$markdown$2d$it$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]({
        html: true,
        linkify: true,
        typographer: true,
        highlight: function(str, lang) {
            if (lang && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highlight$2e$js$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].getLanguage(lang)) {
                try {
                    return `<pre class="hljs"><code class="hljs language-${lang}">${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highlight$2e$js$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].highlight(str, {
                        language: lang
                    }).value}</code></pre>`;
                } catch  {}
            }
            return `<pre class="hljs"><code class="hljs">${str}</code></pre>`;
        }
    });
    // Add custom rules for better rendering
    md.renderer.rules.table_open = ()=>'<div class="table-wrapper"><table class="table">';
    md.renderer.rules.table_close = ()=>'</table></div>';
    // Custom link rendering with security
    const defaultLinkRender = md.renderer.rules.link_open || function(tokens, idx, options, _env, self) {
        return self.renderToken(tokens, idx, options);
    };
    md.renderer.rules.link_open = function(tokens, idx, options, env, self) {
        const token = tokens[idx];
        const hrefIndex = token.attrIndex('href');
        if (hrefIndex >= 0) {
            const href = token.attrs[hrefIndex][1];
            // Add security attributes for external links
            if (href.startsWith('http')) {
                token.attrPush([
                    'target',
                    '_blank'
                ]);
                token.attrPush([
                    'rel',
                    'noopener noreferrer'
                ]);
            }
        }
        return defaultLinkRender(tokens, idx, options, env, self);
    };
    return md;
};
const MarkdownPreview = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ source, className = '', style, ...props }, ref)=>{
    const previewRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef(null);
    // Create markdown renderer instance
    const markdownRenderer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>createMarkdownRenderer(), []);
    // Render markdown to HTML
    const htmlContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!source.trim()) {
            return '<div class="text-muted-foreground italic p-4">Nothing to preview</div>';
        }
        try {
            return markdownRenderer.render(source);
        } catch (error) {
            console.error('Markdown rendering error:', error);
            return '<div class="text-destructive p-4">Error rendering markdown</div>';
        }
    }, [
        source,
        markdownRenderer
    ]);
    // Expose methods via ref
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useImperativeHandle(ref, ()=>({
            getElement: ()=>previewRef.current
        }));
    const previewStyles = {
        padding: '16px',
        height: '100%',
        overflow: 'auto',
        backgroundColor: 'hsl(var(--background))',
        color: 'hsl(var(--foreground))',
        ...style
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: previewRef,
        className: `markdown-preview prose prose-sm dark:prose-invert max-w-none ${className}`,
        style: previewStyles,
        dangerouslySetInnerHTML: {
            __html: htmlContent
        },
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/markdown-editor/components/preview.tsx",
        lineNumber: 96,
        columnNumber: 5
    }, this);
});
MarkdownPreview.displayName = 'MarkdownPreview';
;
}}),
"[project]/src/components/markdown-editor/components/drag-bar.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DragBar": (()=>DragBar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
function DragBar({ height, minHeight, maxHeight, onChange, className = '' }) {
    const [isDragging, setIsDragging] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [startY, setStartY] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [startHeight, setStartHeight] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(height);
    const dragBarRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const handleMouseDown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        event.preventDefault();
        setIsDragging(true);
        setStartY(event.clientY);
        setStartHeight(height);
        // Add cursor style to body
        document.body.style.cursor = 'ns-resize';
        document.body.style.userSelect = 'none';
    }, [
        height
    ]);
    const handleMouseMove = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        if (!isDragging) return;
        const deltaY = event.clientY - startY;
        const newHeight = Math.max(minHeight, Math.min(maxHeight, startHeight + deltaY));
        onChange(newHeight);
    }, [
        isDragging,
        startY,
        startHeight,
        minHeight,
        maxHeight,
        onChange
    ]);
    const handleMouseUp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setIsDragging(false);
        // Remove cursor style from body
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
    }, []);
    // Handle touch events for mobile
    const handleTouchStart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        event.preventDefault();
        const touch = event.touches[0];
        setIsDragging(true);
        setStartY(touch.clientY);
        setStartHeight(height);
    }, [
        height
    ]);
    const handleTouchMove = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        if (!isDragging) return;
        event.preventDefault();
        const touch = event.touches[0];
        const deltaY = touch.clientY - startY;
        const newHeight = Math.max(minHeight, Math.min(maxHeight, startHeight + deltaY));
        onChange(newHeight);
    }, [
        isDragging,
        startY,
        startHeight,
        minHeight,
        maxHeight,
        onChange
    ]);
    const handleTouchEnd = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setIsDragging(false);
    }, []);
    // Add global event listeners
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isDragging) {
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
            document.addEventListener('touchmove', handleTouchMove, {
                passive: false
            });
            document.addEventListener('touchend', handleTouchEnd);
            return ()=>{
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
                document.removeEventListener('touchmove', handleTouchMove);
                document.removeEventListener('touchend', handleTouchEnd);
            };
        }
    }, [
        isDragging,
        handleMouseMove,
        handleMouseUp,
        handleTouchMove,
        handleTouchEnd
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: dragBarRef,
        className: `
        relative h-2 bg-border hover:bg-border/80 cursor-ns-resize
        flex items-center justify-center group
        transition-colors duration-200
        ${isDragging ? 'bg-primary/20' : ''}
        ${className}
      `,
        onMouseDown: handleMouseDown,
        onTouchStart: handleTouchStart,
        role: "separator",
        "aria-orientation": "horizontal",
        "aria-label": "Resize editor height",
        tabIndex: 0,
        onKeyDown: (event)=>{
            if (event.key === 'ArrowUp') {
                event.preventDefault();
                const newHeight = Math.max(minHeight, height - 10);
                onChange(newHeight);
            } else if (event.key === 'ArrowDown') {
                event.preventDefault();
                const newHeight = Math.min(maxHeight, height + 10);
                onChange(newHeight);
            }
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex space-x-1 opacity-60 group-hover:opacity-100 transition-opacity",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-8 h-0.5 bg-current rounded-full"
                    }, void 0, false, {
                        fileName: "[project]/src/components/markdown-editor/components/drag-bar.tsx",
                        lineNumber: 117,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-8 h-0.5 bg-current rounded-full"
                    }, void 0, false, {
                        fileName: "[project]/src/components/markdown-editor/components/drag-bar.tsx",
                        lineNumber: 118,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-8 h-0.5 bg-current rounded-full"
                    }, void 0, false, {
                        fileName: "[project]/src/components/markdown-editor/components/drag-bar.tsx",
                        lineNumber: 119,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/markdown-editor/components/drag-bar.tsx",
                lineNumber: 116,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap",
                children: [
                    "Drag to resize • Use arrow keys • Height: ",
                    height,
                    "px"
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/markdown-editor/components/drag-bar.tsx",
                lineNumber: 123,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/components/drag-bar.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/markdown-editor/hooks/use-theme.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applyThemeVariables": (()=>applyThemeVariables),
    "getColorModeAttribute": (()=>getColorModeAttribute),
    "getThemeVariables": (()=>getThemeVariables),
    "useEditorTheme": (()=>useEditorTheme),
    "useThemeVariables": (()=>useThemeVariables)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
function useEditorTheme() {
    const { theme, setTheme, resolvedTheme, systemTheme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Ensure we're mounted before accessing theme to avoid hydration mismatch
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setMounted(true);
    }, []);
    // Return safe defaults during SSR
    if (!mounted) {
        return {
            theme: 'system',
            resolvedTheme: 'light',
            setTheme: ()=>{},
            systemTheme: undefined
        };
    }
    return {
        theme: theme || 'system',
        resolvedTheme: resolvedTheme || 'light',
        setTheme: setTheme,
        systemTheme: systemTheme
    };
}
function getThemeVariables(resolvedTheme) {
    if (resolvedTheme === 'dark') {
        return {
            '--editor-background': '222.2 84% 4.9%',
            '--editor-foreground': '210 40% 98%',
            '--editor-card': '222.2 84% 4.9%',
            '--editor-card-foreground': '210 40% 98%',
            '--editor-popover': '222.2 84% 4.9%',
            '--editor-popover-foreground': '210 40% 98%',
            '--editor-primary': '210 40% 98%',
            '--editor-primary-foreground': '222.2 84% 4.9%',
            '--editor-secondary': '217.2 32.6% 17.5%',
            '--editor-secondary-foreground': '210 40% 98%',
            '--editor-muted': '217.2 32.6% 17.5%',
            '--editor-muted-foreground': '215 20.2% 65.1%',
            '--editor-accent': '217.2 32.6% 17.5%',
            '--editor-accent-foreground': '210 40% 98%',
            '--editor-destructive': '0 62.8% 30.6%',
            '--editor-destructive-foreground': '210 40% 98%',
            '--editor-border': '217.2 32.6% 17.5%',
            '--editor-input': '217.2 32.6% 17.5%',
            '--editor-ring': '212.7 26.8% 83.9%'
        };
    }
    // Light theme
    return {
        '--editor-background': '0 0% 100%',
        '--editor-foreground': '222.2 84% 4.9%',
        '--editor-card': '0 0% 100%',
        '--editor-card-foreground': '222.2 84% 4.9%',
        '--editor-popover': '0 0% 100%',
        '--editor-popover-foreground': '222.2 84% 4.9%',
        '--editor-primary': '222.2 47.4% 11.2%',
        '--editor-primary-foreground': '210 40% 98%',
        '--editor-secondary': '210 40% 96%',
        '--editor-secondary-foreground': '222.2 84% 4.9%',
        '--editor-muted': '210 40% 96%',
        '--editor-muted-foreground': '215.4 16.3% 46.9%',
        '--editor-accent': '210 40% 96%',
        '--editor-accent-foreground': '222.2 84% 4.9%',
        '--editor-destructive': '0 84.2% 60.2%',
        '--editor-destructive-foreground': '210 40% 98%',
        '--editor-border': '214.3 31.8% 91.4%',
        '--editor-input': '214.3 31.8% 91.4%',
        '--editor-ring': '222.2 84% 4.9%'
    };
}
function applyThemeVariables(element, resolvedTheme) {
    const variables = getThemeVariables(resolvedTheme);
    Object.entries(variables).forEach(([property, value])=>{
        element.style.setProperty(property, value);
    });
}
function getColorModeAttribute(resolvedTheme) {
    return resolvedTheme;
}
function useThemeVariables(containerRef) {
    const { resolvedTheme } = useEditorTheme();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (containerRef.current) {
            applyThemeVariables(containerRef.current, resolvedTheme);
            containerRef.current.setAttribute('data-color-mode', getColorModeAttribute(resolvedTheme));
        }
    }, [
        containerRef,
        resolvedTheme
    ]);
    return {
        resolvedTheme,
        colorMode: getColorModeAttribute(resolvedTheme)
    };
}
}}),
"[project]/src/components/markdown-editor/markdown-editor.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MarkdownEditor": (()=>MarkdownEditor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/context.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/textarea.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$toolbar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/toolbar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$preview$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/preview.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$drag$2d$bar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/drag-bar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/utils/text-api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$hooks$2f$use$2d$theme$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/hooks/use-theme.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
// Internal editor component that uses context
const InternalMarkdownEditor = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ value = '', onChange, onHeightChange, height = 400, minHeight = 200, maxHeight = 800, autoFocus = false, preview = 'live', fullscreen = false, overflow = true, visibleDragbar = true, hideToolbar = false, toolbarBottom = false, enableScroll = true, highlightEnable = true, tabSize = 2, defaultTabEnable = false, commands, extraCommands, commandsFilter, className = '', 'data-color-mode': dataColorMode = 'auto', components, textareaProps, previewOptions, ...props }, ref)=>{
    const { state, dispatch } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEditor"])();
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const textareaRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const previewRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const orchestratorRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isMounted, setIsMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsMounted(true);
    }, []);
    // Destructure value from textareaProps to avoid type conflict
    const { value: textareaValue, ...restTextareaProps } = textareaProps || {};
    // Suppress unused variable warning since this is intentionally extracted to avoid conflicts
    void textareaValue;
    // Theme integration
    const { resolvedTheme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$hooks$2f$use$2d$theme$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useThemeVariables"])(containerRef);
    // Expose methods via ref
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, ()=>({
            focus: ()=>textareaRef.current?.focus(),
            blur: ()=>textareaRef.current?.blur(),
            getState: ()=>state,
            getTextArea: ()=>textareaRef.current?.getTextArea() || null
        }));
    // Initialize editor state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        dispatch({
            markdown: value,
            preview,
            fullscreen,
            height,
            highlightEnable,
            tabSize,
            defaultTabEnable,
            commands: commands || state.commands,
            extraCommands: extraCommands || state.extraCommands,
            container: containerRef.current
        });
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []) // Only run on mount - intentionally ignoring dependencies
    ;
    // Update state when props change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (value !== state.markdown) {
            dispatch({
                markdown: value
            });
        }
    }, [
        value,
        state.markdown,
        dispatch
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (preview !== state.preview) {
            dispatch({
                preview
            });
        }
    }, [
        preview,
        state.preview,
        dispatch
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (fullscreen !== state.fullscreen) {
            dispatch({
                fullscreen
            });
        }
    }, [
        fullscreen,
        state.fullscreen,
        dispatch
    ]);
    // Handle browser fullscreen API
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time truthy", 1) return; // Ensure this runs only on the client
        "TURBOPACK unreachable";
        const container = undefined;
        // Check if Fullscreen API is supported
        const doc = undefined;
        const fullscreenEnabled = undefined;
        // Get the appropriate fullscreen element property
        const getFullscreenElement = undefined;
        // Handle fullscreen change
        const handleFullscreenChange = undefined;
    }, [
        state.fullscreen,
        dispatch
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (height !== state.height) {
            dispatch({
                height
            });
            onHeightChange?.(height, state.height, state);
        }
    }, [
        height,
        state.height,
        dispatch,
        onHeightChange,
        state
    ]);
    // Handle textarea change
    const handleTextareaChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        const newValue = event.target.value;
        dispatch({
            markdown: newValue
        });
        onChange?.(newValue, event, state);
        textareaProps?.onChange?.(event);
    }, [
        onChange,
        state,
        textareaProps,
        dispatch
    ]);
    // Execute command
    const executeCommand = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((command)=>{
        if (!command.execute) return;
        const textarea = textareaRef.current?.getTextArea();
        if (!textarea) return;
        if (!orchestratorRef.current) {
            orchestratorRef.current = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CommandOrchestrator"](textarea);
        }
        const textApi = orchestratorRef.current.getTextApi();
        const textState = orchestratorRef.current.getState();
        if (textState) {
            command.execute(textState, textApi, dispatch, {
                markdown: state.markdown,
                preview: state.preview,
                fullscreen: state.fullscreen,
                height: state.height
            });
        }
    }, [
        state,
        dispatch
    ]);
    // Handle container click to close popups
    const handleContainerClick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        dispatch({
            barPopup: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["closeAllPopups"])(state.barPopup)
        });
    }, [
        state.barPopup,
        dispatch
    ]);
    // Handle scroll synchronization
    const handleTextareaScroll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        if (!enableScroll) return;
        const textarea = event.currentTarget;
        const previewElement = previewRef.current?.getElement();
        if (previewElement && textarea) {
            const scrollPercentage = textarea.scrollTop / (textarea.scrollHeight - textarea.clientHeight);
            const previewScrollTop = scrollPercentage * (previewElement.scrollHeight - previewElement.clientHeight);
            previewElement.scrollTop = previewScrollTop;
        }
    }, [
        enableScroll
    ]);
    // Handle drag bar change
    const handleDragBarChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((newHeight)=>{
        dispatch({
            height: newHeight
        });
        onHeightChange?.(newHeight, state.height, state);
    }, [
        dispatch,
        onHeightChange,
        state
    ]);
    // Filter commands if needed
    const filteredCommands = commands || state.commands.filter((cmd)=>commandsFilter ? commandsFilter(cmd, false) !== false : true);
    const filteredExtraCommands = extraCommands || state.extraCommands.filter((cmd)=>commandsFilter ? commandsFilter(cmd, true) !== false : true);
    // Container classes
    const containerClasses = [
        'markdown-editor',
        'border border-border rounded-lg overflow-hidden',
        'bg-background text-foreground',
        'flex flex-col',
        'w-full max-w-[90%] mx-auto',
        state.fullscreen ? 'fixed inset-0 z-50 fullscreen' : 'relative',
        state.preview === 'edit' ? 'editor-only' : '',
        state.preview === 'preview' ? 'preview-only' : '',
        state.preview === 'live' ? 'editor-live' : '',
        // Mobile optimizations
        'sm:rounded-lg',
        className
    ].filter(Boolean).join(' ');
    const editorHeight = typeof state.height === 'number' ? `${state.height}px` : state.height;
    const editorMarkup = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: containerRef,
        className: containerClasses,
        style: state.fullscreen ? {} : {
            height: editorHeight
        },
        "data-color-mode": dataColorMode || 'auto',
        "data-theme": resolvedTheme,
        onClick: handleContainerClick,
        ...props,
        children: [
            !hideToolbar && !toolbarBottom && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$toolbar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Toolbar"], {
                        commands: filteredCommands,
                        extraCommands: filteredExtraCommands,
                        executeCommand: executeCommand,
                        overflow: overflow,
                        toolbarBottom: false,
                        state: state,
                        dispatch: dispatch
                    }, void 0, false, {
                        fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
                        lineNumber: 313,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            display: 'none'
                        },
                        children: "Title 1 Title 2 Title 3 Title 4 Title 5 Title 6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
                        lineNumber: 323,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `
        flex-1 flex overflow-hidden editor-content
        ${state.preview === 'live' ? 'md:flex-row flex-col' : 'flex-row'}
      `,
                children: [
                    (state.preview === 'edit' || state.preview === 'live') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `
            ${state.preview === 'live' ? 'md:w-[60%] w-full md:h-full h-1/2' : 'w-full h-full'}
            relative editor-pane
          `,
                        children: components?.textarea ? components.textarea({
                            value: state.markdown || '',
                            onChange: handleTextareaChange,
                            onScroll: handleTextareaScroll,
                            highlightEnable,
                            tabSize,
                            defaultTabEnable,
                            autoFocus,
                            ...restTextareaProps
                        }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Textarea"], {
                            ref: textareaRef,
                            value: state.markdown || '',
                            onChange: handleTextareaChange,
                            onScroll: handleTextareaScroll,
                            highlightEnable: highlightEnable,
                            tabSize: tabSize,
                            defaultTabEnable: defaultTabEnable,
                            autoFocus: autoFocus,
                            ...restTextareaProps
                        }, void 0, false, {
                            fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
                            lineNumber: 355,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
                        lineNumber: 336,
                        columnNumber: 11
                    }, this),
                    state.preview === 'live' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "md:w-px md:h-auto w-full h-px bg-border editor-divider"
                    }, void 0, false, {
                        fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
                        lineNumber: 372,
                        columnNumber: 11
                    }, this),
                    (state.preview === 'preview' || state.preview === 'live') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `
            ${state.preview === 'live' ? 'md:w-[40%] w-full md:h-full h-1/2' : 'w-full h-full'}
            relative preview-pane
          `,
                        children: components?.preview ? components.preview(state.markdown || '', state, dispatch) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$preview$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MarkdownPreview"], {
                            ref: previewRef,
                            source: state.markdown || '',
                            ...previewOptions
                        }, void 0, false, {
                            fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
                            lineNumber: 387,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
                        lineNumber: 377,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
                lineNumber: 330,
                columnNumber: 7
            }, this),
            visibleDragbar && !state.fullscreen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$drag$2d$bar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DragBar"], {
                height: typeof state.height === 'number' ? state.height : 400,
                minHeight: minHeight,
                maxHeight: maxHeight,
                onChange: handleDragBarChange
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
                lineNumber: 399,
                columnNumber: 9
            }, this),
            !hideToolbar && toolbarBottom && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$toolbar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Toolbar"], {
                commands: filteredCommands,
                extraCommands: filteredExtraCommands,
                executeCommand: executeCommand,
                overflow: overflow,
                toolbarBottom: true,
                state: state,
                dispatch: dispatch
            }, void 0, false, {
                fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
                lineNumber: 409,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
        lineNumber: 301,
        columnNumber: 5
    }, this);
    if (!isMounted) {
        return editorMarkup;
    }
    if (state.fullscreen) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createPortal"])(editorMarkup, document.body);
    }
    return editorMarkup;
});
InternalMarkdownEditor.displayName = 'InternalMarkdownEditor';
// Main editor component with provider
const MarkdownEditor = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EditorProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(InternalMarkdownEditor, {
            ref: ref,
            ...props
        }, void 0, false, {
            fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
            lineNumber: 439,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/markdown-editor/markdown-editor.tsx",
        lineNumber: 438,
        columnNumber: 5
    }, this);
});
MarkdownEditor.displayName = 'MarkdownEditor';
;
}}),
"[project]/src/components/markdown-editor/utils/markdown.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Markdown processing utilities
 */ /**
 * Extract frontmatter from markdown content
 */ __turbopack_context__.s({
    "cleanMarkdown": (()=>cleanMarkdown),
    "extractFrontmatter": (()=>extractFrontmatter),
    "extractHeadings": (()=>extractHeadings),
    "generateTableOfContents": (()=>generateTableOfContents),
    "getMarkdownStats": (()=>getMarkdownStats),
    "markdownToPlainText": (()=>markdownToPlainText),
    "updateFrontmatter": (()=>updateFrontmatter),
    "validateMarkdown": (()=>validateMarkdown)
});
function extractFrontmatter(markdown) {
    const frontmatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/;
    const match = markdown.match(frontmatterRegex);
    if (!match) {
        return {
            frontmatter: {},
            content: markdown
        };
    }
    const [, frontmatterStr, content] = match;
    try {
        // Simple YAML-like parsing for basic frontmatter
        const frontmatter = {};
        frontmatterStr.split('\n').forEach((line)=>{
            const colonIndex = line.indexOf(':');
            if (colonIndex > 0) {
                const key = line.substring(0, colonIndex).trim();
                const value = line.substring(colonIndex + 1).trim();
                // Remove quotes if present
                const cleanValue = value.replace(/^["']|["']$/g, '');
                // Try to parse as number or boolean
                if (cleanValue === 'true') {
                    frontmatter[key] = true;
                } else if (cleanValue === 'false') {
                    frontmatter[key] = false;
                } else if (!isNaN(Number(cleanValue)) && cleanValue !== '') {
                    frontmatter[key] = Number(cleanValue);
                } else {
                    frontmatter[key] = cleanValue;
                }
            }
        });
        return {
            frontmatter,
            content
        };
    } catch (error) {
        console.warn('Failed to parse frontmatter:', error);
        return {
            frontmatter: {},
            content: markdown
        };
    }
}
function updateFrontmatter(markdown, updates) {
    const { frontmatter, content } = extractFrontmatter(markdown);
    const updatedFrontmatter = {
        ...frontmatter,
        ...updates
    };
    if (Object.keys(updatedFrontmatter).length === 0) {
        return content;
    }
    const frontmatterStr = Object.entries(updatedFrontmatter).map(([key, value])=>{
        if (typeof value === 'string') {
            return `${key}: "${value}"`;
        }
        return `${key}: ${value}`;
    }).join('\n');
    return `---\n${frontmatterStr}\n---\n${content}`;
}
function extractHeadings(markdown) {
    const lines = markdown.split('\n');
    const headings = [];
    lines.forEach((line, index)=>{
        const match = line.match(/^(#{1,6})\s+(.+)$/);
        if (match) {
            const level = match[1].length;
            const text = match[2].trim();
            const id = text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
            headings.push({
                level,
                text,
                id,
                line: index + 1
            });
        }
    });
    return headings;
}
function generateTableOfContents(markdown) {
    const headings = extractHeadings(markdown);
    if (headings.length === 0) {
        return '';
    }
    const toc = headings.map((heading)=>{
        const indent = '  '.repeat(heading.level - 1);
        return `${indent}- [${heading.text}](#${heading.id})`;
    }).join('\n');
    return `## Table of Contents\n\n${toc}\n`;
}
function getMarkdownStats(markdown) {
    const { content } = extractFrontmatter(markdown);
    // Remove code blocks for word counting
    const withoutCodeBlocks = content.replace(/```[\s\S]*?```/g, '');
    // Remove inline code for word counting
    const withoutInlineCode = withoutCodeBlocks.replace(/`[^`]+`/g, '');
    // Count words (split by whitespace and filter empty strings)
    const words = withoutInlineCode.trim().split(/\s+/).filter((word)=>word.length > 0).length;
    const characters = content.length;
    const charactersNoSpaces = content.replace(/\s/g, '').length;
    // Count paragraphs (non-empty lines that aren't headings, lists, etc.)
    const paragraphs = content.split('\n').filter((line)=>{
        const trimmed = line.trim();
        return trimmed.length > 0 && !trimmed.startsWith('#') && !trimmed.startsWith('-') && !trimmed.startsWith('*') && !trimmed.startsWith('+') && !trimmed.match(/^\d+\./) && !trimmed.startsWith('>');
    }).length;
    const headings = (content.match(/^#{1,6}\s+.+$/gm) || []).length;
    const links = (content.match(/\[([^\]]+)\]\(([^)]+)\)/g) || []).length;
    const images = (content.match(/!\[([^\]]*)\]\(([^)]+)\)/g) || []).length;
    const codeBlocks = (content.match(/```[\s\S]*?```/g) || []).length;
    const lists = (content.match(/^[\s]*[-*+]\s+/gm) || []).length + (content.match(/^[\s]*\d+\.\s+/gm) || []).length;
    // Estimate reading time (average 200 words per minute)
    const readingTime = Math.ceil(words / 200);
    return {
        words,
        characters,
        charactersNoSpaces,
        paragraphs,
        headings,
        links,
        images,
        codeBlocks,
        lists,
        readingTime
    };
}
function validateMarkdown(markdown) {
    const errors = [];
    const lines = markdown.split('\n');
    lines.forEach((line, index)=>{
        const lineNumber = index + 1;
        // Check for unmatched brackets in links
        const linkMatches = line.match(/\[([^\]]*)\]/g);
        if (linkMatches) {
            linkMatches.forEach((match)=>{
                const column = line.indexOf(match) + 1;
                if (!line.includes(`${match}(`)) {
                    errors.push({
                        line: lineNumber,
                        column,
                        message: 'Link text without URL',
                        type: 'warning'
                    });
                }
            });
        }
        // Check for unmatched code blocks
        const codeBlockMatches = line.match(/```/g);
        if (codeBlockMatches && codeBlockMatches.length % 2 !== 0) {
            // This is a simple check - a more sophisticated parser would track state
            const column = line.indexOf('```') + 1;
            errors.push({
                line: lineNumber,
                column,
                message: 'Potentially unmatched code block',
                type: 'warning'
            });
        }
        // Check for empty headings
        if (line.match(/^#{1,6}\s*$/)) {
            errors.push({
                line: lineNumber,
                column: 1,
                message: 'Empty heading',
                type: 'warning'
            });
        }
    });
    return errors;
}
function cleanMarkdown(markdown) {
    return markdown// Remove excessive blank lines
    .replace(/\n{3,}/g, '\n\n')// Trim whitespace from lines
    .split('\n').map((line)=>line.trimEnd()).join('\n')// Remove trailing whitespace
    .trim();
}
function markdownToPlainText(markdown) {
    const { content } = extractFrontmatter(markdown);
    return content// Remove code blocks
    .replace(/```[\s\S]*?```/g, '')// Remove inline code
    .replace(/`[^`]+`/g, '')// Remove images
    .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '$1')// Remove links but keep text
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1')// Remove headings markers
    .replace(/^#{1,6}\s+/gm, '')// Remove bold/italic
    .replace(/\*\*([^*]+)\*\*/g, '$1').replace(/\*([^*]+)\*/g, '$1')// Remove strikethrough
    .replace(/~~([^~]+)~~/g, '$1')// Remove blockquotes
    .replace(/^>\s+/gm, '')// Remove list markers
    .replace(/^[\s]*[-*+]\s+/gm, '').replace(/^[\s]*\d+\.\s+/gm, '')// Remove horizontal rules
    .replace(/^---+$/gm, '')// Clean up whitespace
    .replace(/\n{2,}/g, '\n\n').trim();
}
}}),
"[project]/src/components/markdown-editor/utils/keyboard.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Keyboard shortcut utilities
 */ __turbopack_context__.s({
    "COMMON_SHORTCUTS": (()=>COMMON_SHORTCUTS),
    "addGlobalShortcuts": (()=>addGlobalShortcuts),
    "createShortcutHandler": (()=>createShortcutHandler),
    "formatShortcutForTooltip": (()=>formatShortcutForTooltip),
    "getPrimaryShortcut": (()=>getPrimaryShortcut),
    "getShortcutDescription": (()=>getShortcutDescription),
    "matchesShortcut": (()=>matchesShortcut),
    "parseShortcut": (()=>parseShortcut),
    "shouldReceiveShortcuts": (()=>shouldReceiveShortcuts)
});
function parseShortcut(shortcut) {
    const parts = shortcut.toLowerCase().split('+');
    const result = {
        key: parts[parts.length - 1]
    };
    parts.slice(0, -1).forEach((modifier)=>{
        switch(modifier){
            case 'ctrl':
                result.ctrl = true;
                break;
            case 'cmd':
            case 'meta':
                result.meta = true;
                break;
            case 'shift':
                result.shift = true;
                break;
            case 'alt':
                result.alt = true;
                break;
        }
    });
    return result;
}
function matchesShortcut(event, shortcut) {
    const parsed = parseShortcut(shortcut);
    // Normalize key names
    const eventKey = event.key.toLowerCase();
    const shortcutKey = parsed.key.toLowerCase();
    // Check key match
    const keyMatches = eventKey === shortcutKey || shortcutKey === 'space' && eventKey === ' ' || shortcutKey === 'enter' && eventKey === 'enter' || shortcutKey === 'tab' && eventKey === 'tab' || shortcutKey === 'escape' && eventKey === 'escape';
    if (!keyMatches) return false;
    // Check modifiers
    const ctrlMatches = !!parsed.ctrl === event.ctrlKey;
    const metaMatches = !!parsed.meta === event.metaKey;
    const shiftMatches = !!parsed.shift === event.shiftKey;
    const altMatches = !!parsed.alt === event.altKey;
    return ctrlMatches && metaMatches && shiftMatches && altMatches;
}
function getShortcutDescription(shortcut) {
    const parsed = parseShortcut(shortcut);
    const parts = [];
    // Detect platform
    const isMac = typeof navigator !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    if (parsed.ctrl) {
        parts.push(isMac ? '⌃' : 'Ctrl');
    }
    if (parsed.meta) {
        parts.push(isMac ? '⌘' : 'Win');
    }
    if (parsed.alt) {
        parts.push(isMac ? '⌥' : 'Alt');
    }
    if (parsed.shift) {
        parts.push(isMac ? '⇧' : 'Shift');
    }
    // Capitalize and format key
    let key = parsed.key;
    if (key.length === 1) {
        key = key.toUpperCase();
    } else {
        key = key.charAt(0).toUpperCase() + key.slice(1);
    }
    parts.push(key);
    return parts.join(isMac ? '' : '+');
}
function createShortcutHandler(shortcuts) {
    return (event)=>{
        for (const [shortcut, handler] of Object.entries(shortcuts)){
            if (matchesShortcut(event, shortcut)) {
                event.preventDefault();
                handler();
                break;
            }
        }
    };
}
const COMMON_SHORTCUTS = {
    BOLD: [
        'ctrl+b',
        'cmd+b'
    ],
    ITALIC: [
        'ctrl+i',
        'cmd+i'
    ],
    UNDERLINE: [
        'ctrl+u',
        'cmd+u'
    ],
    STRIKETHROUGH: [
        'ctrl+shift+x',
        'cmd+shift+x'
    ],
    CODE: [
        'ctrl+`',
        'cmd+`'
    ],
    CODE_BLOCK: [
        'ctrl+shift+c',
        'cmd+shift+c'
    ],
    LINK: [
        'ctrl+k',
        'cmd+k'
    ],
    IMAGE: [
        'ctrl+shift+i',
        'cmd+shift+i'
    ],
    HEADING_1: [
        'ctrl+1',
        'cmd+1'
    ],
    HEADING_2: [
        'ctrl+2',
        'cmd+2'
    ],
    HEADING_3: [
        'ctrl+3',
        'cmd+3'
    ],
    UNORDERED_LIST: [
        'ctrl+shift+8',
        'cmd+shift+8'
    ],
    ORDERED_LIST: [
        'ctrl+shift+7',
        'cmd+shift+7'
    ],
    QUOTE: [
        'ctrl+shift+.',
        'cmd+shift+.'
    ],
    HORIZONTAL_RULE: [
        'ctrl+shift+-',
        'cmd+shift+-'
    ],
    SAVE: [
        'ctrl+s',
        'cmd+s'
    ],
    UNDO: [
        'ctrl+z',
        'cmd+z'
    ],
    REDO: [
        'ctrl+y',
        'cmd+y',
        'ctrl+shift+z',
        'cmd+shift+z'
    ],
    FIND: [
        'ctrl+f',
        'cmd+f'
    ],
    REPLACE: [
        'ctrl+h',
        'cmd+h'
    ],
    FULLSCREEN: [
        'F11'
    ],
    PREVIEW: [
        'ctrl+shift+p',
        'cmd+shift+p'
    ],
    LIVE_PREVIEW: [
        'ctrl+shift+l',
        'cmd+shift+l'
    ]
};
function getPrimaryShortcut(shortcuts) {
    const isMac = typeof navigator !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    // Find the first shortcut that matches the platform
    for (const shortcut of shortcuts){
        const hasCmd = shortcut.includes('cmd');
        const hasCtrl = shortcut.includes('ctrl');
        if (isMac && hasCmd) return shortcut;
        if (!isMac && hasCtrl) return shortcut;
    }
    // Fallback to first shortcut
    return shortcuts[0];
}
function shouldReceiveShortcuts(element) {
    const tagName = element.tagName.toLowerCase();
    // Don't handle shortcuts in form inputs (except our editor)
    if ([
        'input',
        'select',
        'option'
    ].includes(tagName)) {
        return false;
    }
    // Don't handle shortcuts in contenteditable elements (except our editor)
    if (element.getAttribute('contenteditable') === 'true') {
        return false;
    }
    // Allow shortcuts in our markdown editor textarea
    if (element.closest('.markdown-editor')) {
        return true;
    }
    // Don't handle shortcuts in other textareas
    if (tagName === 'textarea') {
        return false;
    }
    return true;
}
function addGlobalShortcuts(shortcuts, options = {}) {
    const { preventDefault = true, checkTarget = true } = options;
    const handler = (event)=>{
        // Check if the target should receive shortcuts
        if (checkTarget && event.target && !shouldReceiveShortcuts(event.target)) {
            return;
        }
        for (const [shortcut, callback] of Object.entries(shortcuts)){
            if (matchesShortcut(event, shortcut)) {
                if (preventDefault) {
                    event.preventDefault();
                }
                callback();
                break;
            }
        }
    };
    document.addEventListener('keydown', handler);
    // Return cleanup function
    return ()=>{
        document.removeEventListener('keydown', handler);
    };
}
function formatShortcutForTooltip(shortcuts) {
    const primary = getPrimaryShortcut(shortcuts);
    return getShortcutDescription(primary);
}
}}),
"[project]/src/components/markdown-editor/utils/validation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Validation utilities for markdown editor
 */ __turbopack_context__.s({
    "getValidationSummary": (()=>getValidationSummary),
    "validateFrontmatter": (()=>validateFrontmatter),
    "validateMarkdown": (()=>validateMarkdown)
});
function validateMarkdown(content, options = {}) {
    const errors = [];
    const lines = content.split('\n');
    const { maxLength = 50000, maxLines = 1000, allowHtml = false, requireFrontmatter = false, allowedTags = [], blockedWords = [], checkLinks = true, checkImages = true } = options;
    // Check content length
    if (content.length > maxLength) {
        errors.push({
            line: 1,
            column: 1,
            message: `Content exceeds maximum length of ${maxLength} characters`,
            type: 'error',
            code: 'MAX_LENGTH_EXCEEDED'
        });
    }
    // Check line count
    if (lines.length > maxLines) {
        errors.push({
            line: maxLines + 1,
            column: 1,
            message: `Content exceeds maximum of ${maxLines} lines`,
            type: 'error',
            code: 'MAX_LINES_EXCEEDED'
        });
    }
    // Check for frontmatter if required
    if (requireFrontmatter && !content.startsWith('---')) {
        errors.push({
            line: 1,
            column: 1,
            message: 'Frontmatter is required',
            type: 'error',
            code: 'MISSING_FRONTMATTER'
        });
    }
    // Validate each line
    lines.forEach((line, index)=>{
        const lineNumber = index + 1;
        // Check for HTML tags if not allowed
        if (!allowHtml) {
            const htmlMatches = line.match(/<[^>]+>/g);
            if (htmlMatches) {
                htmlMatches.forEach((tag)=>{
                    const column = line.indexOf(tag) + 1;
                    const tagName = tag.match(/<\/?([a-zA-Z][a-zA-Z0-9]*)/)?.[1];
                    if (tagName && !allowedTags.includes(tagName.toLowerCase())) {
                        errors.push({
                            line: lineNumber,
                            column,
                            message: `HTML tag '${tagName}' is not allowed`,
                            type: 'error',
                            code: 'HTML_NOT_ALLOWED'
                        });
                    }
                });
            }
        }
        // Check for blocked words
        if (blockedWords.length > 0) {
            blockedWords.forEach((word)=>{
                const regex = new RegExp(`\\b${word}\\b`, 'gi');
                let match;
                while((match = regex.exec(line)) !== null){
                    errors.push({
                        line: lineNumber,
                        column: match.index + 1,
                        message: `Blocked word '${word}' found`,
                        type: 'warning',
                        code: 'BLOCKED_WORD'
                    });
                }
            });
        }
        // Check links
        if (checkLinks) {
            const linkMatches = line.matchAll(/\[([^\]]*)\]\(([^)]+)\)/g);
            for (const match of linkMatches){
                const [, text, url] = match;
                const column = match.index + 1;
                // Check for empty link text
                if (!text.trim()) {
                    errors.push({
                        line: lineNumber,
                        column,
                        message: 'Link has empty text',
                        type: 'warning',
                        code: 'EMPTY_LINK_TEXT'
                    });
                }
                // Check for empty URL
                if (!url.trim()) {
                    errors.push({
                        line: lineNumber,
                        column: column + text.length + 3,
                        message: 'Link has empty URL',
                        type: 'error',
                        code: 'EMPTY_LINK_URL'
                    });
                }
                // Check for malformed URLs
                if (url.trim() && !isValidUrl(url)) {
                    errors.push({
                        line: lineNumber,
                        column: column + text.length + 3,
                        message: 'Link URL appears to be malformed',
                        type: 'warning',
                        code: 'MALFORMED_URL'
                    });
                }
            }
        }
        // Check images
        if (checkImages) {
            const imageMatches = line.matchAll(/!\[([^\]]*)\]\(([^)]+)\)/g);
            for (const match of imageMatches){
                const [, alt, src] = match;
                const column = match.index + 1;
                // Check for empty alt text
                if (!alt.trim()) {
                    errors.push({
                        line: lineNumber,
                        column,
                        message: 'Image has empty alt text',
                        type: 'warning',
                        code: 'EMPTY_IMAGE_ALT'
                    });
                }
                // Check for empty src
                if (!src.trim()) {
                    errors.push({
                        line: lineNumber,
                        column: column + alt.length + 4,
                        message: 'Image has empty source',
                        type: 'error',
                        code: 'EMPTY_IMAGE_SRC'
                    });
                }
            }
        }
        // Check for unmatched code blocks
        const codeBlockMatches = line.match(/```/g);
        if (codeBlockMatches && codeBlockMatches.length % 2 !== 0) {
            const column = line.indexOf('```') + 1;
            errors.push({
                line: lineNumber,
                column,
                message: 'Potentially unmatched code block',
                type: 'warning',
                code: 'UNMATCHED_CODE_BLOCK'
            });
        }
        // Check for empty headings
        if (line.match(/^#{1,6}\s*$/)) {
            errors.push({
                line: lineNumber,
                column: 1,
                message: 'Empty heading',
                type: 'warning',
                code: 'EMPTY_HEADING'
            });
        }
        // Check for excessive heading levels
        const headingMatch = line.match(/^(#{7,})\s/);
        if (headingMatch) {
            errors.push({
                line: lineNumber,
                column: 1,
                message: 'Heading level too deep (maximum is 6)',
                type: 'error',
                code: 'HEADING_TOO_DEEP'
            });
        }
        // Check for trailing whitespace
        if (line.endsWith(' ') || line.endsWith('\t')) {
            errors.push({
                line: lineNumber,
                column: line.length,
                message: 'Line has trailing whitespace',
                type: 'info',
                code: 'TRAILING_WHITESPACE'
            });
        }
    });
    return errors;
}
/**
 * Check if a URL is valid
 */ function isValidUrl(url) {
    // Allow relative URLs
    if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
        return true;
    }
    // Allow anchor links
    if (url.startsWith('#')) {
        return true;
    }
    // Check absolute URLs
    try {
        new URL(url);
        return true;
    } catch  {
        return false;
    }
}
function validateFrontmatter(frontmatter, requiredFields = [], allowedFields = []) {
    const errors = [];
    const lines = frontmatter.split('\n');
    const fields = new Set();
    lines.forEach((line, index)=>{
        const lineNumber = index + 1;
        const colonIndex = line.indexOf(':');
        if (line.trim() && colonIndex > 0) {
            const key = line.substring(0, colonIndex).trim();
            const value = line.substring(colonIndex + 1).trim();
            fields.add(key);
            // Check if field is allowed
            if (allowedFields.length > 0 && !allowedFields.includes(key)) {
                errors.push({
                    line: lineNumber,
                    column: 1,
                    message: `Field '${key}' is not allowed`,
                    type: 'warning',
                    code: 'FIELD_NOT_ALLOWED'
                });
            }
            // Check for empty values
            if (!value) {
                errors.push({
                    line: lineNumber,
                    column: colonIndex + 2,
                    message: `Field '${key}' has empty value`,
                    type: 'warning',
                    code: 'EMPTY_FIELD_VALUE'
                });
            }
        } else if (line.trim() && !line.includes(':')) {
            errors.push({
                line: lineNumber,
                column: 1,
                message: 'Invalid frontmatter syntax',
                type: 'error',
                code: 'INVALID_FRONTMATTER_SYNTAX'
            });
        }
    });
    // Check for required fields
    requiredFields.forEach((field)=>{
        if (!fields.has(field)) {
            errors.push({
                line: 1,
                column: 1,
                message: `Required field '${field}' is missing`,
                type: 'error',
                code: 'MISSING_REQUIRED_FIELD'
            });
        }
    });
    return errors;
}
function getValidationSummary(errors) {
    const errorCount = errors.filter((e)=>e.type === 'error').length;
    const warningCount = errors.filter((e)=>e.type === 'warning').length;
    const infoCount = errors.filter((e)=>e.type === 'info').length;
    return {
        errorCount,
        warningCount,
        infoCount,
        hasErrors: errorCount > 0,
        hasWarnings: warningCount > 0
    };
}
}}),
"[project]/src/components/markdown-editor/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Main editor component
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$markdown$2d$editor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/markdown-editor.tsx [app-ssr] (ecmascript)");
// Context and hooks
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/context.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$hooks$2f$use$2d$theme$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/hooks/use-theme.ts [app-ssr] (ecmascript)");
// Components
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/textarea.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$toolbar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/toolbar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$preview$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/preview.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$drag$2d$bar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/drag-bar.tsx [app-ssr] (ecmascript)");
// Commands
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$commands$2f$index$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/commands/index.tsx [app-ssr] (ecmascript)");
// Utilities
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/utils/text-api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$markdown$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/utils/markdown.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$keyboard$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/utils/keyboard.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/utils/validation.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/src/components/markdown-editor/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$markdown$2d$editor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/markdown-editor.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/context.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$hooks$2f$use$2d$theme$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/hooks/use-theme.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/textarea.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$toolbar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/toolbar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$preview$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/preview.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$components$2f$drag$2d$bar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/components/drag-bar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$commands$2f$index$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/commands/index.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$text$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/utils/text-api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$markdown$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/utils/markdown.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$keyboard$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/utils/keyboard.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$utils$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/utils/validation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/app/test-fullscreen/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TestFullscreenPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$markdown$2d$editor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/markdown-editor/markdown-editor.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
const testContent = `# Fullscreen Test

This is a test page to verify that the fullscreen functionality works correctly.

## Test Instructions

1. Click the fullscreen button in the toolbar (⛶ icon)
2. Verify that:
   - The editor takes up the full screen
   - All text content is visible and readable
   - You can type and edit text normally
   - Syntax highlighting works (if enabled)
   - The cursor is visible
   - Scrolling works properly

## Sample Content

Here's some sample markdown content to test with:

### Code Block

\`\`\`javascript
function testFullscreen() {
  console.log('Testing fullscreen mode');
  return 'All text should be visible!';
}
\`\`\`

### List

- Item 1
- Item 2
- Item 3

### Table

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Data 1   | Data 2   | Data 3   |
| Data 4   | Data 5   | Data 6   |

### Long Text

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.

### More Content

Add more content here to test scrolling in fullscreen mode...

**Bold text should be visible**
*Italic text should be visible*
\`Inline code should be visible\`

> Blockquotes should be visible and properly formatted

1. Numbered lists
2. Should also work
3. In fullscreen mode

---

End of test content. If you can see this text clearly in fullscreen mode, the fix is working!
`;
function TestFullscreenPage() {
    const [content, setContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(testContent);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-background p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-6xl mx-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    className: "text-3xl font-bold mb-4",
                    children: "Fullscreen Test Page"
                }, void 0, false, {
                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                    lineNumber: 78,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-4 p-4 bg-card border border-border rounded-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-semibold mb-2",
                            children: "Test Instructions"
                        }, void 0, false, {
                            fileName: "[project]/src/app/test-fullscreen/page.tsx",
                            lineNumber: 81,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ol", {
                            className: "list-decimal list-inside space-y-1 text-sm",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "Click the fullscreen button (⛶) in the editor toolbar"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                                    lineNumber: 83,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "Verify that all text content is visible and readable"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                                    lineNumber: 84,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "Test typing and editing functionality"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                                    lineNumber: 85,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "Check that scrolling works properly"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                                    lineNumber: 86,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "Press F11 or click the fullscreen button again to exit"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                                    lineNumber: 87,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/test-fullscreen/page.tsx",
                            lineNumber: 82,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                    lineNumber: 80,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "border border-border rounded-lg overflow-hidden",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$markdown$2d$editor$2f$markdown$2d$editor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MarkdownEditor"], {
                        value: content,
                        onChange: (value)=>setContent(value || ''),
                        height: "600px",
                        preview: "live",
                        highlightEnable: true
                    }, void 0, false, {
                        fileName: "[project]/src/app/test-fullscreen/page.tsx",
                        lineNumber: 92,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                    lineNumber: 91,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-4 p-4 bg-muted rounded-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "font-semibold mb-2",
                            children: "Expected Behavior:"
                        }, void 0, false, {
                            fileName: "[project]/src/app/test-fullscreen/page.tsx",
                            lineNumber: 102,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            className: "text-sm space-y-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "✅ Text should be clearly visible (not transparent)"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                                    lineNumber: 104,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "✅ Cursor should be visible when typing"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                                    lineNumber: 105,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "✅ Syntax highlighting should work (if enabled)"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                                    lineNumber: 106,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "✅ Editor should fill the entire screen"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                                    lineNumber: 107,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "✅ Scrolling should work smoothly"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                                    lineNumber: 108,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "✅ All toolbar buttons should be functional"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                                    lineNumber: 109,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/test-fullscreen/page.tsx",
                            lineNumber: 103,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/test-fullscreen/page.tsx",
                    lineNumber: 101,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/test-fullscreen/page.tsx",
            lineNumber: 77,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/test-fullscreen/page.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e0db056e._.js.map