(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[466],{255:(e,r,t)=>{"use strict";function n(e){let{moduleIds:r}=e;return null}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"PreloadChunks",{enumerable:!0,get:function(){return n}}),t(5155),t(7650),t(5744),t(589)},2146:(e,r,t)=>{"use strict";function n(e){let{reason:r,children:t}=e;return t}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"BailoutToCSR",{enumerable:!0,get:function(){return n}}),t(5262)},2643:(e,r,t)=>{"use strict";t.d(r,{U:()=>s});var n=t(3865);function s(){return(0,n.createBrowserClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNxb2l4dmdtcm9lamdhZWJ4eWVxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNjU0ODksImV4cCI6MjA2Njc0MTQ4OX0.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU")}},3063:(e,r,t)=>{"use strict";t.d(r,{D:()=>l});var n=t(5155),s=t(5028),o=t(2115);let i=(0,s.default)(()=>Promise.all([t.e(827),t.e(895)]).then(t.bind(t,895)),{loadableGenerated:{webpack:()=>[null]},ssr:!1,loading:()=>(0,n.jsx)("div",{className:"w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse min-h-[400px] flex items-center justify-center",children:(0,n.jsx)("div",{className:"text-muted-foreground",children:"Loading editor..."})})});function l(e){let{value:r="",onChange:t,minHeight:s="500px",placeholder:l="Start writing your amazing content..."}=e,[a,d]=(0,o.useState)(!1);if((0,o.useEffect)(()=>{d(!0)},[]),!a)return(0,n.jsx)("div",{className:"w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse flex items-center justify-center",style:{minHeight:s},children:(0,n.jsx)("div",{className:"text-muted-foreground",children:"Loading editor..."})});try{return(0,n.jsx)("div",{className:"w-full md-editor-wrapper",style:{minHeight:s},"data-color-mode":"auto",children:(0,n.jsx)(i,{value:r,onChange:e=>null==t?void 0:t(e||""),preview:"live",hideToolbar:!1,visibleDragbar:!1,textareaProps:{placeholder:l,style:{fontSize:14,lineHeight:1.6,fontFamily:'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',color:"inherit"}},style:{backgroundColor:"hsl(var(--background))",color:"hsl(var(--foreground))",minHeight:s}})})}catch(e){return console.error("Error rendering MDEditor:",e),(0,n.jsxs)("div",{className:"w-full p-4 border border-input bg-background text-foreground rounded-md",children:[(0,n.jsx)("div",{className:"text-red-500 mb-2",children:"Error loading editor"}),(0,n.jsx)("textarea",{value:r,onChange:e=>null==t?void 0:t(e.target.value),placeholder:l,className:"w-full h-full bg-transparent border-0 outline-0 resize-none text-foreground",style:{minHeight:"calc(".concat(s," - 2rem)")}})]})}}},3322:(e,r,t)=>{Promise.resolve().then(t.bind(t,9788))},4054:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{bindSnapshot:function(){return i},createAsyncLocalStorage:function(){return o},createSnapshot:function(){return l}});let t=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class n{disable(){throw t}getStore(){}run(){throw t}exit(){throw t}enterWith(){throw t}static bind(e){return e}}let s="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function o(){return s?new s:new n}function i(e){return s?s.bind(e):n.bind(e)}function l(){return s?s.snapshot():function(e,...r){return e(...r)}}},5028:(e,r,t)=>{"use strict";t.d(r,{default:()=>s.a});var n=t(6645),s=t.n(n)},5744:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"workAsyncStorage",{enumerable:!0,get:function(){return n.workAsyncStorageInstance}});let n=t(7828)},6645:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s}});let n=t(8229)._(t(7357));function s(e,r){var t;let s={};"function"==typeof e&&(s.loader=e);let o={...s,...r};return(0,n.default)({...o,modules:null==(t=o.loadableGenerated)?void 0:t.modules})}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},7357:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a}});let n=t(5155),s=t(2115),o=t(2146);function i(e){return{default:e&&"default"in e?e.default:e}}t(255);let l={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},a=function(e){let r={...l,...e},t=(0,s.lazy)(()=>r.loader().then(i)),a=r.loading;function d(e){let i=a?(0,n.jsx)(a,{isLoading:!0,pastDelay:!0,error:null}):null,l=!r.ssr||!!r.loading,d=l?s.Suspense:s.Fragment,c=r.ssr?(0,n.jsxs)(n.Fragment,{children:[null,(0,n.jsx)(t,{...e})]}):(0,n.jsx)(o.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(t,{...e})});return(0,n.jsx)(d,{...l?{fallback:i}:{},children:c})}return d.displayName="LoadableComponent",d}},7828:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"workAsyncStorageInstance",{enumerable:!0,get:function(){return n}});let n=(0,t(4054).createAsyncLocalStorage)()},9788:(e,r,t)=>{"use strict";t.d(r,{default:()=>c});var n=t(5155),s=t(2115),o=t(5695),i=t(6874),l=t.n(i),a=t(3063),d=t(2643);function c(e){let{post:r}=e,[t,i]=(0,s.useState)(r.title),[c,u]=(0,s.useState)(r.content),[m,h]=(0,s.useState)(!1),[x,f]=(0,s.useState)(""),p=(0,o.useRouter)(),g=async e=>{if(e.preventDefault(),!t.trim()||!c.trim())return void f("Title and content are required");h(!0),f("");try{let e=(0,d.U)(),{error:n}=await e.from("posts").update({title:t.trim(),content:c.trim(),updated_at:new Date().toISOString()}).eq("id",r.id);if(n)throw n;p.push("/posts/".concat(r.id)),p.refresh()}catch(e){console.error("Error updating post:",JSON.stringify(e,Object.getOwnPropertyNames(e))),f("Error updating post. Please try again.")}finally{h(!1)}};return(0,n.jsxs)("div",{className:"animate-fade-in",children:[(0,n.jsx)("div",{className:"mb-6 sm:mb-8",children:(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground mb-2",children:[(0,n.jsx)("span",{children:"Editing Post"}),(0,n.jsxs)("span",{className:"flex items-center",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Auto-save enabled"]})]}),(0,n.jsx)("div",{className:"w-full bg-muted rounded-full h-1",children:(0,n.jsx)("div",{className:"bg-primary h-1 rounded-full w-2/3 transition-all duration-300"})})]}),(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)(l(),{href:"/admin/manage-posts",className:"text-muted-foreground hover:text-foreground transition-colors font-medium inline-flex items-center px-3 py-2 rounded-lg hover:bg-muted/50",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),(0,n.jsx)("span",{className:"hidden sm:inline",children:"Back to manage"}),(0,n.jsx)("span",{className:"sm:hidden",children:"Back"})]}),(0,n.jsxs)("button",{type:"button",onClick:()=>{confirm("Are you sure you want to discard all changes?")&&(i(r.title),u(r.content),f(""))},className:"text-muted-foreground hover:text-destructive transition-colors font-medium inline-flex items-center px-3 py-2 rounded-lg hover:bg-destructive/10",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,n.jsx)("span",{className:"hidden sm:inline",children:"Reset"}),(0,n.jsx)("span",{className:"sm:hidden",children:"↻"})]})]})]})}),(0,n.jsxs)("form",{onSubmit:g,className:"space-y-8",children:[x&&(0,n.jsx)("div",{className:"bg-destructive/10 border border-destructive/20 text-destructive px-6 py-4 rounded-xl animate-slide-in shadow-sm",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"w-10 h-10 bg-destructive/10 rounded-full flex items-center justify-center mr-3",children:(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium",children:"Error"}),(0,n.jsx)("p",{className:"text-sm",children:x})]})]})}),(0,n.jsxs)("div",{className:"bg-card rounded-2xl border border-border shadow-xl overflow-hidden",children:[(0,n.jsxs)("div",{className:"bg-gradient-to-r from-primary/5 to-secondary/5 px-8 py-6 border-b border-border",children:[(0,n.jsxs)("h2",{className:"text-xl font-bold text-foreground flex items-center",children:[(0,n.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center mr-3",children:(0,n.jsx)("svg",{className:"w-5 h-5 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),"Edit Post Details"]}),(0,n.jsx)("p",{className:"text-muted-foreground mt-2",children:"Update your post title and content below"})]}),(0,n.jsxs)("div",{className:"p-8 space-y-8",children:[(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("label",{htmlFor:"title",className:"flex items-center text-sm font-semibold text-foreground",children:[(0,n.jsx)("div",{className:"w-6 h-6 bg-primary/10 rounded-lg flex items-center justify-center mr-2",children:(0,n.jsx)("svg",{className:"w-3 h-3 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a.997.997 0 01-1.414 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z"})})}),"Post Title",(0,n.jsx)("span",{className:"text-destructive ml-1",children:"*"})]}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("input",{type:"text",id:"title",value:t,onChange:e=>i(e.target.value),className:"w-full px-4 py-4 border border-input bg-background text-foreground rounded-xl focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-200 text-lg font-medium placeholder:text-muted-foreground/60",placeholder:"Enter an engaging title for your post...",disabled:m}),(0,n.jsxs)("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground",children:[t.length,"/100"]})]})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("label",{htmlFor:"content",className:"flex items-center text-sm font-semibold text-foreground",children:[(0,n.jsx)("div",{className:"w-6 h-6 bg-primary/10 rounded-lg flex items-center justify-center mr-2",children:(0,n.jsx)("svg",{className:"w-3 h-3 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),"Post Content",(0,n.jsx)("span",{className:"text-destructive ml-1",children:"*"})]}),(0,n.jsx)("div",{className:"border border-input rounded-xl overflow-hidden shadow-sm bg-background",children:(0,n.jsx)(a.D,{value:c,onChange:u,minHeight:"650px",placeholder:"Start writing your amazing content..."})}),(0,n.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground mt-3 px-1",children:[(0,n.jsx)("span",{children:"Markdown supported"}),(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsxs)("span",{children:[c.split(" ").length," words"]}),(0,n.jsxs)("span",{children:[c.length," characters"]}),(0,n.jsxs)("span",{children:[Math.ceil(c.split(" ").length/200)," min read"]})]})]})]})]})]}),(0,n.jsx)("div",{className:"bg-card rounded-2xl border border-border p-4 sm:p-6 shadow-lg",children:(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsx)("button",{type:"submit",disabled:m||!t.trim()||!c.trim(),className:"w-full sm:w-auto bg-primary text-primary-foreground px-8 py-4 rounded-xl hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-semibold shadow-lg hover:shadow-xl inline-flex items-center justify-center group",children:m?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-3"}),"Updating Post..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("svg",{className:"w-5 h-5 mr-3 group-hover:scale-110 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"})}),"Update Post"]})})})})]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[874,271,441,684,358],()=>r(3322)),_N_E=e.O()}]);