{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/mdurl/encode.js"], "sourcesContent": ["\n'use strict';\n\n\nvar encodeCache = {};\n\n\n// Create a lookup array where anything but characters in `chars` string\n// and alphanumeric chars is percent-encoded.\n//\nfunction getEncodeCache(exclude) {\n  var i, ch, cache = encodeCache[exclude];\n  if (cache) { return cache; }\n\n  cache = encodeCache[exclude] = [];\n\n  for (i = 0; i < 128; i++) {\n    ch = String.fromCharCode(i);\n\n    if (/^[0-9a-z]$/i.test(ch)) {\n      // always allow unencoded alphanumeric characters\n      cache.push(ch);\n    } else {\n      cache.push('%' + ('0' + i.toString(16).toUpperCase()).slice(-2));\n    }\n  }\n\n  for (i = 0; i < exclude.length; i++) {\n    cache[exclude.charCodeAt(i)] = exclude[i];\n  }\n\n  return cache;\n}\n\n\n// Encode unsafe characters with percent-encoding, skipping already\n// encoded sequences.\n//\n//  - string       - string to encode\n//  - exclude      - list of characters to ignore (in addition to a-zA-Z0-9)\n//  - keepEscaped  - don't encode '%' in a correct escape sequence (default: true)\n//\nfunction encode(string, exclude, keepEscaped) {\n  var i, l, code, nextCode, cache,\n      result = '';\n\n  if (typeof exclude !== 'string') {\n    // encode(string, keepEscaped)\n    keepEscaped  = exclude;\n    exclude = encode.defaultChars;\n  }\n\n  if (typeof keepEscaped === 'undefined') {\n    keepEscaped = true;\n  }\n\n  cache = getEncodeCache(exclude);\n\n  for (i = 0, l = string.length; i < l; i++) {\n    code = string.charCodeAt(i);\n\n    if (keepEscaped && code === 0x25 /* % */ && i + 2 < l) {\n      if (/^[0-9a-f]{2}$/i.test(string.slice(i + 1, i + 3))) {\n        result += string.slice(i, i + 3);\n        i += 2;\n        continue;\n      }\n    }\n\n    if (code < 128) {\n      result += cache[code];\n      continue;\n    }\n\n    if (code >= 0xD800 && code <= 0xDFFF) {\n      if (code >= 0xD800 && code <= 0xDBFF && i + 1 < l) {\n        nextCode = string.charCodeAt(i + 1);\n        if (nextCode >= 0xDC00 && nextCode <= 0xDFFF) {\n          result += encodeURIComponent(string[i] + string[i + 1]);\n          i++;\n          continue;\n        }\n      }\n      result += '%EF%BF%BD';\n      continue;\n    }\n\n    result += encodeURIComponent(string[i]);\n  }\n\n  return result;\n}\n\nencode.defaultChars   = \";/?:@&=+$,-_.!~*'()#\";\nencode.componentChars = \"-_.!~*'()\";\n\n\nmodule.exports = encode;\n"], "names": [], "mappings": "AACA;AAGA,IAAI,cAAc,CAAC;AAGnB,wEAAwE;AACxE,6CAA6C;AAC7C,EAAE;AACF,SAAS,eAAe,OAAO;IAC7B,IAAI,GAAG,IAAI,QAAQ,WAAW,CAAC,QAAQ;IACvC,IAAI,OAAO;QAAE,OAAO;IAAO;IAE3B,QAAQ,WAAW,CAAC,QAAQ,GAAG,EAAE;IAEjC,IAAK,IAAI,GAAG,IAAI,KAAK,IAAK;QACxB,KAAK,OAAO,YAAY,CAAC;QAEzB,IAAI,cAAc,IAAI,CAAC,KAAK;YAC1B,iDAAiD;YACjD,MAAM,IAAI,CAAC;QACb,OAAO;YACL,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;QAC/D;IACF;IAEA,IAAK,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACnC,KAAK,CAAC,QAAQ,UAAU,CAAC,GAAG,GAAG,OAAO,CAAC,EAAE;IAC3C;IAEA,OAAO;AACT;AAGA,mEAAmE;AACnE,qBAAqB;AACrB,EAAE;AACF,qCAAqC;AACrC,4EAA4E;AAC5E,kFAAkF;AAClF,EAAE;AACF,SAAS,OAAO,MAAM,EAAE,OAAO,EAAE,WAAW;IAC1C,IAAI,GAAG,GAAG,MAAM,UAAU,OACtB,SAAS;IAEb,IAAI,OAAO,YAAY,UAAU;QAC/B,8BAA8B;QAC9B,cAAe;QACf,UAAU,OAAO,YAAY;IAC/B;IAEA,IAAI,OAAO,gBAAgB,aAAa;QACtC,cAAc;IAChB;IAEA,QAAQ,eAAe;IAEvB,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,IAAK;QACzC,OAAO,OAAO,UAAU,CAAC;QAEzB,IAAI,eAAe,SAAS,KAAK,KAAK,OAAM,IAAI,IAAI,GAAG;YACrD,IAAI,iBAAiB,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,GAAG,IAAI,KAAK;gBACrD,UAAU,OAAO,KAAK,CAAC,GAAG,IAAI;gBAC9B,KAAK;gBACL;YACF;QACF;QAEA,IAAI,OAAO,KAAK;YACd,UAAU,KAAK,CAAC,KAAK;YACrB;QACF;QAEA,IAAI,QAAQ,UAAU,QAAQ,QAAQ;YACpC,IAAI,QAAQ,UAAU,QAAQ,UAAU,IAAI,IAAI,GAAG;gBACjD,WAAW,OAAO,UAAU,CAAC,IAAI;gBACjC,IAAI,YAAY,UAAU,YAAY,QAAQ;oBAC5C,UAAU,mBAAmB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;oBACtD;oBACA;gBACF;YACF;YACA,UAAU;YACV;QACF;QAEA,UAAU,mBAAmB,MAAM,CAAC,EAAE;IACxC;IAEA,OAAO;AACT;AAEA,OAAO,YAAY,GAAK;AACxB,OAAO,cAAc,GAAG;AAGxB,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/mdurl/decode.js"], "sourcesContent": ["\n'use strict';\n\n\n/* eslint-disable no-bitwise */\n\nvar decodeCache = {};\n\nfunction getDecodeCache(exclude) {\n  var i, ch, cache = decodeCache[exclude];\n  if (cache) { return cache; }\n\n  cache = decodeCache[exclude] = [];\n\n  for (i = 0; i < 128; i++) {\n    ch = String.fromCharCode(i);\n    cache.push(ch);\n  }\n\n  for (i = 0; i < exclude.length; i++) {\n    ch = exclude.charCodeAt(i);\n    cache[ch] = '%' + ('0' + ch.toString(16).toUpperCase()).slice(-2);\n  }\n\n  return cache;\n}\n\n\n// Decode percent-encoded string.\n//\nfunction decode(string, exclude) {\n  var cache;\n\n  if (typeof exclude !== 'string') {\n    exclude = decode.defaultChars;\n  }\n\n  cache = getDecodeCache(exclude);\n\n  return string.replace(/(%[a-f0-9]{2})+/gi, function(seq) {\n    var i, l, b1, b2, b3, b4, chr,\n        result = '';\n\n    for (i = 0, l = seq.length; i < l; i += 3) {\n      b1 = parseInt(seq.slice(i + 1, i + 3), 16);\n\n      if (b1 < 0x80) {\n        result += cache[b1];\n        continue;\n      }\n\n      if ((b1 & 0xE0) === 0xC0 && (i + 3 < l)) {\n        // 110xxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n\n        if ((b2 & 0xC0) === 0x80) {\n          chr = ((b1 << 6) & 0x7C0) | (b2 & 0x3F);\n\n          if (chr < 0x80) {\n            result += '\\ufffd\\ufffd';\n          } else {\n            result += String.fromCharCode(chr);\n          }\n\n          i += 3;\n          continue;\n        }\n      }\n\n      if ((b1 & 0xF0) === 0xE0 && (i + 6 < l)) {\n        // 1110xxxx 10xxxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n        b3 = parseInt(seq.slice(i + 7, i + 9), 16);\n\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80) {\n          chr = ((b1 << 12) & 0xF000) | ((b2 << 6) & 0xFC0) | (b3 & 0x3F);\n\n          if (chr < 0x800 || (chr >= 0xD800 && chr <= 0xDFFF)) {\n            result += '\\ufffd\\ufffd\\ufffd';\n          } else {\n            result += String.fromCharCode(chr);\n          }\n\n          i += 6;\n          continue;\n        }\n      }\n\n      if ((b1 & 0xF8) === 0xF0 && (i + 9 < l)) {\n        // 111110xx 10xxxxxx 10xxxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n        b3 = parseInt(seq.slice(i + 7, i + 9), 16);\n        b4 = parseInt(seq.slice(i + 10, i + 12), 16);\n\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80 && (b4 & 0xC0) === 0x80) {\n          chr = ((b1 << 18) & 0x1C0000) | ((b2 << 12) & 0x3F000) | ((b3 << 6) & 0xFC0) | (b4 & 0x3F);\n\n          if (chr < 0x10000 || chr > 0x10FFFF) {\n            result += '\\ufffd\\ufffd\\ufffd\\ufffd';\n          } else {\n            chr -= 0x10000;\n            result += String.fromCharCode(0xD800 + (chr >> 10), 0xDC00 + (chr & 0x3FF));\n          }\n\n          i += 9;\n          continue;\n        }\n      }\n\n      result += '\\ufffd';\n    }\n\n    return result;\n  });\n}\n\n\ndecode.defaultChars   = ';/?:@&=+$,#';\ndecode.componentChars = '';\n\n\nmodule.exports = decode;\n"], "names": [], "mappings": "AACA;AAGA,6BAA6B,GAE7B,IAAI,cAAc,CAAC;AAEnB,SAAS,eAAe,OAAO;IAC7B,IAAI,GAAG,IAAI,QAAQ,WAAW,CAAC,QAAQ;IACvC,IAAI,OAAO;QAAE,OAAO;IAAO;IAE3B,QAAQ,WAAW,CAAC,QAAQ,GAAG,EAAE;IAEjC,IAAK,IAAI,GAAG,IAAI,KAAK,IAAK;QACxB,KAAK,OAAO,YAAY,CAAC;QACzB,MAAM,IAAI,CAAC;IACb;IAEA,IAAK,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACnC,KAAK,QAAQ,UAAU,CAAC;QACxB,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,IAAI,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;IACjE;IAEA,OAAO;AACT;AAGA,iCAAiC;AACjC,EAAE;AACF,SAAS,OAAO,MAAM,EAAE,OAAO;IAC7B,IAAI;IAEJ,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU,OAAO,YAAY;IAC/B;IAEA,QAAQ,eAAe;IAEvB,OAAO,OAAO,OAAO,CAAC,qBAAqB,SAAS,GAAG;QACrD,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KACtB,SAAS;QAEb,IAAK,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAI,GAAG,KAAK,EAAG;YACzC,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,IAAI;YAEvC,IAAI,KAAK,MAAM;gBACb,UAAU,KAAK,CAAC,GAAG;gBACnB;YACF;YAEA,IAAI,CAAC,KAAK,IAAI,MAAM,QAAS,IAAI,IAAI,GAAI;gBACvC,oBAAoB;gBACpB,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,IAAI;gBAEvC,IAAI,CAAC,KAAK,IAAI,MAAM,MAAM;oBACxB,MAAM,AAAE,MAAM,IAAK,QAAU,KAAK;oBAElC,IAAI,MAAM,MAAM;wBACd,UAAU;oBACZ,OAAO;wBACL,UAAU,OAAO,YAAY,CAAC;oBAChC;oBAEA,KAAK;oBACL;gBACF;YACF;YAEA,IAAI,CAAC,KAAK,IAAI,MAAM,QAAS,IAAI,IAAI,GAAI;gBACvC,6BAA6B;gBAC7B,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,IAAI;gBACvC,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,IAAI;gBAEvC,IAAI,CAAC,KAAK,IAAI,MAAM,QAAQ,CAAC,KAAK,IAAI,MAAM,MAAM;oBAChD,MAAM,AAAE,MAAM,KAAM,SAAW,AAAC,MAAM,IAAK,QAAU,KAAK;oBAE1D,IAAI,MAAM,SAAU,OAAO,UAAU,OAAO,QAAS;wBACnD,UAAU;oBACZ,OAAO;wBACL,UAAU,OAAO,YAAY,CAAC;oBAChC;oBAEA,KAAK;oBACL;gBACF;YACF;YAEA,IAAI,CAAC,KAAK,IAAI,MAAM,QAAS,IAAI,IAAI,GAAI;gBACvC,sCAAsC;gBACtC,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,IAAI;gBACvC,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,IAAI;gBACvC,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,KAAK;gBAEzC,IAAI,CAAC,KAAK,IAAI,MAAM,QAAQ,CAAC,KAAK,IAAI,MAAM,QAAQ,CAAC,KAAK,IAAI,MAAM,MAAM;oBACxE,MAAM,AAAE,MAAM,KAAM,WAAa,AAAC,MAAM,KAAM,UAAY,AAAC,MAAM,IAAK,QAAU,KAAK;oBAErF,IAAI,MAAM,WAAW,MAAM,UAAU;wBACnC,UAAU;oBACZ,OAAO;wBACL,OAAO;wBACP,UAAU,OAAO,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,MAAM,KAAK;oBAC3E;oBAEA,KAAK;oBACL;gBACF;YACF;YAEA,UAAU;QACZ;QAEA,OAAO;IACT;AACF;AAGA,OAAO,YAAY,GAAK;AACxB,OAAO,cAAc,GAAG;AAGxB,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/mdurl/format.js"], "sourcesContent": ["\n'use strict';\n\n\nmodule.exports = function format(url) {\n  var result = '';\n\n  result += url.protocol || '';\n  result += url.slashes ? '//' : '';\n  result += url.auth ? url.auth + '@' : '';\n\n  if (url.hostname && url.hostname.indexOf(':') !== -1) {\n    // ipv6 address\n    result += '[' + url.hostname + ']';\n  } else {\n    result += url.hostname || '';\n  }\n\n  result += url.port ? ':' + url.port : '';\n  result += url.pathname || '';\n  result += url.search || '';\n  result += url.hash || '';\n\n  return result;\n};\n"], "names": [], "mappings": "AACA;AAGA,OAAO,OAAO,GAAG,SAAS,OAAO,GAAG;IAClC,IAAI,SAAS;IAEb,UAAU,IAAI,QAAQ,IAAI;IAC1B,UAAU,IAAI,OAAO,GAAG,OAAO;IAC/B,UAAU,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,MAAM;IAEtC,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG;QACpD,eAAe;QACf,UAAU,MAAM,IAAI,QAAQ,GAAG;IACjC,OAAO;QACL,UAAU,IAAI,QAAQ,IAAI;IAC5B;IAEA,UAAU,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI,GAAG;IACtC,UAAU,IAAI,QAAQ,IAAI;IAC1B,UAAU,IAAI,MAAM,IAAI;IACxB,UAAU,IAAI,IAAI,IAAI;IAEtB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/mdurl/parse.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n//\n// Changes from joyent/node:\n//\n// 1. No leading slash in paths,\n//    e.g. in `url.parse('http://foo?bar')` pathname is ``, not `/`\n//\n// 2. Backslashes are not replaced with slashes,\n//    so `http:\\\\example.org\\` is treated like a relative path\n//\n// 3. Trailing colon is treated like a part of the path,\n//    i.e. in `http://example.org:foo` pathname is `:foo`\n//\n// 4. Nothing is URL-encoded in the resulting object,\n//    (in joyent/node some chars in auth and paths are encoded)\n//\n// 5. `url.parse()` does not have `parseQueryString` argument\n//\n// 6. Removed extraneous result properties: `host`, `path`, `query`, etc.,\n//    which can be constructed using other parts of the url.\n//\n\n\nfunction Url() {\n  this.protocol = null;\n  this.slashes = null;\n  this.auth = null;\n  this.port = null;\n  this.hostname = null;\n  this.hash = null;\n  this.search = null;\n  this.pathname = null;\n}\n\n// Reference: RFC 3986, RFC 1808, RFC 2396\n\n// define these here so at least they only have to be\n// compiled once on the first module load.\nvar protocolPattern = /^([a-z0-9.+-]+:)/i,\n    portPattern = /:[0-9]*$/,\n\n    // Special case for a simple path URL\n    simplePathPattern = /^(\\/\\/?(?!\\/)[^\\?\\s]*)(\\?[^\\s]*)?$/,\n\n    // RFC 2396: characters reserved for delimiting URLs.\n    // We actually just auto-escape these.\n    delims = [ '<', '>', '\"', '`', ' ', '\\r', '\\n', '\\t' ],\n\n    // RFC 2396: characters not allowed for various reasons.\n    unwise = [ '{', '}', '|', '\\\\', '^', '`' ].concat(delims),\n\n    // Allowed by RFCs, but cause of XSS attacks.  Always escape these.\n    autoEscape = [ '\\'' ].concat(unwise),\n    // Characters that are never ever allowed in a hostname.\n    // Note that any invalid chars are also handled, but these\n    // are the ones that are *expected* to be seen, so we fast-path\n    // them.\n    nonHostChars = [ '%', '/', '?', ';', '#' ].concat(autoEscape),\n    hostEndingChars = [ '/', '?', '#' ],\n    hostnameMaxLen = 255,\n    hostnamePartPattern = /^[+a-z0-9A-Z_-]{0,63}$/,\n    hostnamePartStart = /^([+a-z0-9A-Z_-]{0,63})(.*)$/,\n    // protocols that can allow \"unsafe\" and \"unwise\" chars.\n    /* eslint-disable no-script-url */\n    // protocols that never have a hostname.\n    hostlessProtocol = {\n      'javascript': true,\n      'javascript:': true\n    },\n    // protocols that always contain a // bit.\n    slashedProtocol = {\n      'http': true,\n      'https': true,\n      'ftp': true,\n      'gopher': true,\n      'file': true,\n      'http:': true,\n      'https:': true,\n      'ftp:': true,\n      'gopher:': true,\n      'file:': true\n    };\n    /* eslint-enable no-script-url */\n\nfunction urlParse(url, slashesDenoteHost) {\n  if (url && url instanceof Url) { return url; }\n\n  var u = new Url();\n  u.parse(url, slashesDenoteHost);\n  return u;\n}\n\nUrl.prototype.parse = function(url, slashesDenoteHost) {\n  var i, l, lowerProto, hec, slashes,\n      rest = url;\n\n  // trim before proceeding.\n  // This is to support parse stuff like \"  http://foo.com  \\n\"\n  rest = rest.trim();\n\n  if (!slashesDenoteHost && url.split('#').length === 1) {\n    // Try fast path regexp\n    var simplePath = simplePathPattern.exec(rest);\n    if (simplePath) {\n      this.pathname = simplePath[1];\n      if (simplePath[2]) {\n        this.search = simplePath[2];\n      }\n      return this;\n    }\n  }\n\n  var proto = protocolPattern.exec(rest);\n  if (proto) {\n    proto = proto[0];\n    lowerProto = proto.toLowerCase();\n    this.protocol = proto;\n    rest = rest.substr(proto.length);\n  }\n\n  // figure out if it's got a host\n  // user@server is *always* interpreted as a hostname, and url\n  // resolution will treat //foo/bar as host=foo,path=bar because that's\n  // how the browser resolves relative URLs.\n  if (slashesDenoteHost || proto || rest.match(/^\\/\\/[^@\\/]+@[^@\\/]+/)) {\n    slashes = rest.substr(0, 2) === '//';\n    if (slashes && !(proto && hostlessProtocol[proto])) {\n      rest = rest.substr(2);\n      this.slashes = true;\n    }\n  }\n\n  if (!hostlessProtocol[proto] &&\n      (slashes || (proto && !slashedProtocol[proto]))) {\n\n    // there's a hostname.\n    // the first instance of /, ?, ;, or # ends the host.\n    //\n    // If there is an @ in the hostname, then non-host chars *are* allowed\n    // to the left of the last @ sign, unless some host-ending character\n    // comes *before* the @-sign.\n    // URLs are obnoxious.\n    //\n    // ex:\n    // http://a@b@c/ => user:a@b host:c\n    // http://a@b?@c => user:a host:c path:/?@c\n\n    // v0.12 TODO(isaacs): This is not quite how Chrome does things.\n    // Review our test case against browsers more comprehensively.\n\n    // find the first instance of any hostEndingChars\n    var hostEnd = -1;\n    for (i = 0; i < hostEndingChars.length; i++) {\n      hec = rest.indexOf(hostEndingChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec;\n      }\n    }\n\n    // at this point, either we have an explicit point where the\n    // auth portion cannot go past, or the last @ char is the decider.\n    var auth, atSign;\n    if (hostEnd === -1) {\n      // atSign can be anywhere.\n      atSign = rest.lastIndexOf('@');\n    } else {\n      // atSign must be in auth portion.\n      // http://a@b/c@d => host:b auth:a path:/c@d\n      atSign = rest.lastIndexOf('@', hostEnd);\n    }\n\n    // Now we have a portion which is definitely the auth.\n    // Pull that off.\n    if (atSign !== -1) {\n      auth = rest.slice(0, atSign);\n      rest = rest.slice(atSign + 1);\n      this.auth = auth;\n    }\n\n    // the host is the remaining to the left of the first non-host char\n    hostEnd = -1;\n    for (i = 0; i < nonHostChars.length; i++) {\n      hec = rest.indexOf(nonHostChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec;\n      }\n    }\n    // if we still have not hit it, then the entire thing is a host.\n    if (hostEnd === -1) {\n      hostEnd = rest.length;\n    }\n\n    if (rest[hostEnd - 1] === ':') { hostEnd--; }\n    var host = rest.slice(0, hostEnd);\n    rest = rest.slice(hostEnd);\n\n    // pull out port.\n    this.parseHost(host);\n\n    // we've indicated that there is a hostname,\n    // so even if it's empty, it has to be present.\n    this.hostname = this.hostname || '';\n\n    // if hostname begins with [ and ends with ]\n    // assume that it's an IPv6 address.\n    var ipv6Hostname = this.hostname[0] === '[' &&\n        this.hostname[this.hostname.length - 1] === ']';\n\n    // validate a little.\n    if (!ipv6Hostname) {\n      var hostparts = this.hostname.split(/\\./);\n      for (i = 0, l = hostparts.length; i < l; i++) {\n        var part = hostparts[i];\n        if (!part) { continue; }\n        if (!part.match(hostnamePartPattern)) {\n          var newpart = '';\n          for (var j = 0, k = part.length; j < k; j++) {\n            if (part.charCodeAt(j) > 127) {\n              // we replace non-ASCII char with a temporary placeholder\n              // we need this to make sure size of hostname is not\n              // broken by replacing non-ASCII by nothing\n              newpart += 'x';\n            } else {\n              newpart += part[j];\n            }\n          }\n          // we test again with ASCII char only\n          if (!newpart.match(hostnamePartPattern)) {\n            var validParts = hostparts.slice(0, i);\n            var notHost = hostparts.slice(i + 1);\n            var bit = part.match(hostnamePartStart);\n            if (bit) {\n              validParts.push(bit[1]);\n              notHost.unshift(bit[2]);\n            }\n            if (notHost.length) {\n              rest = notHost.join('.') + rest;\n            }\n            this.hostname = validParts.join('.');\n            break;\n          }\n        }\n      }\n    }\n\n    if (this.hostname.length > hostnameMaxLen) {\n      this.hostname = '';\n    }\n\n    // strip [ and ] from the hostname\n    // the host field still retains them, though\n    if (ipv6Hostname) {\n      this.hostname = this.hostname.substr(1, this.hostname.length - 2);\n    }\n  }\n\n  // chop off from the tail first.\n  var hash = rest.indexOf('#');\n  if (hash !== -1) {\n    // got a fragment string.\n    this.hash = rest.substr(hash);\n    rest = rest.slice(0, hash);\n  }\n  var qm = rest.indexOf('?');\n  if (qm !== -1) {\n    this.search = rest.substr(qm);\n    rest = rest.slice(0, qm);\n  }\n  if (rest) { this.pathname = rest; }\n  if (slashedProtocol[lowerProto] &&\n      this.hostname && !this.pathname) {\n    this.pathname = '';\n  }\n\n  return this;\n};\n\nUrl.prototype.parseHost = function(host) {\n  var port = portPattern.exec(host);\n  if (port) {\n    port = port[0];\n    if (port !== ':') {\n      this.port = port.substr(1);\n    }\n    host = host.substr(0, host.length - port.length);\n  }\n  if (host) { this.hostname = host; }\n};\n\nmodule.exports = urlParse;\n"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAEzC;AAEA,EAAE;AACF,4BAA4B;AAC5B,EAAE;AACF,gCAAgC;AAChC,mEAAmE;AACnE,EAAE;AACF,gDAAgD;AAChD,8DAA8D;AAC9D,EAAE;AACF,wDAAwD;AACxD,yDAAyD;AACzD,EAAE;AACF,qDAAqD;AACrD,+DAA+D;AAC/D,EAAE;AACF,6DAA6D;AAC7D,EAAE;AACF,0EAA0E;AAC1E,4DAA4D;AAC5D,EAAE;AAGF,SAAS;IACP,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,QAAQ,GAAG;AAClB;AAEA,0CAA0C;AAE1C,qDAAqD;AACrD,0CAA0C;AAC1C,IAAI,kBAAkB,qBAClB,cAAc,YAEd,qCAAqC;AACrC,oBAAoB,sCAEpB,qDAAqD;AACrD,sCAAsC;AACtC,SAAS;IAAE;IAAK;IAAK;IAAK;IAAK;IAAK;IAAM;IAAM;CAAM,EAEtD,wDAAwD;AACxD,SAAS;IAAE;IAAK;IAAK;IAAK;IAAM;IAAK;CAAK,CAAC,MAAM,CAAC,SAElD,mEAAmE;AACnE,aAAa;IAAE;CAAM,CAAC,MAAM,CAAC,SAC7B,wDAAwD;AACxD,0DAA0D;AAC1D,+DAA+D;AAC/D,QAAQ;AACR,eAAe;IAAE;IAAK;IAAK;IAAK;IAAK;CAAK,CAAC,MAAM,CAAC,aAClD,kBAAkB;IAAE;IAAK;IAAK;CAAK,EACnC,iBAAiB,KACjB,sBAAsB,0BACtB,oBAAoB,gCACpB,wDAAwD;AACxD,gCAAgC,GAChC,wCAAwC;AACxC,mBAAmB;IACjB,cAAc;IACd,eAAe;AACjB,GACA,0CAA0C;AAC1C,kBAAkB;IAChB,QAAQ;IACR,SAAS;IACT,OAAO;IACP,UAAU;IACV,QAAQ;IACR,SAAS;IACT,UAAU;IACV,QAAQ;IACR,WAAW;IACX,SAAS;AACX;AACA,+BAA+B,GAEnC,SAAS,SAAS,GAAG,EAAE,iBAAiB;IACtC,IAAI,OAAO,eAAe,KAAK;QAAE,OAAO;IAAK;IAE7C,IAAI,IAAI,IAAI;IACZ,EAAE,KAAK,CAAC,KAAK;IACb,OAAO;AACT;AAEA,IAAI,SAAS,CAAC,KAAK,GAAG,SAAS,GAAG,EAAE,iBAAiB;IACnD,IAAI,GAAG,GAAG,YAAY,KAAK,SACvB,OAAO;IAEX,0BAA0B;IAC1B,6DAA6D;IAC7D,OAAO,KAAK,IAAI;IAEhB,IAAI,CAAC,qBAAqB,IAAI,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;QACrD,uBAAuB;QACvB,IAAI,aAAa,kBAAkB,IAAI,CAAC;QACxC,IAAI,YAAY;YACd,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,EAAE;YAC7B,IAAI,UAAU,CAAC,EAAE,EAAE;gBACjB,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,EAAE;YAC7B;YACA,OAAO,IAAI;QACb;IACF;IAEA,IAAI,QAAQ,gBAAgB,IAAI,CAAC;IACjC,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,EAAE;QAChB,aAAa,MAAM,WAAW;QAC9B,IAAI,CAAC,QAAQ,GAAG;QAChB,OAAO,KAAK,MAAM,CAAC,MAAM,MAAM;IACjC;IAEA,gCAAgC;IAChC,6DAA6D;IAC7D,sEAAsE;IACtE,0CAA0C;IAC1C,IAAI,qBAAqB,SAAS,KAAK,KAAK,CAAC,yBAAyB;QACpE,UAAU,KAAK,MAAM,CAAC,GAAG,OAAO;QAChC,IAAI,WAAW,CAAC,CAAC,SAAS,gBAAgB,CAAC,MAAM,GAAG;YAClD,OAAO,KAAK,MAAM,CAAC;YACnB,IAAI,CAAC,OAAO,GAAG;QACjB;IACF;IAEA,IAAI,CAAC,gBAAgB,CAAC,MAAM,IACxB,CAAC,WAAY,SAAS,CAAC,eAAe,CAAC,MAAM,AAAC,GAAG;QAEnD,sBAAsB;QACtB,qDAAqD;QACrD,EAAE;QACF,sEAAsE;QACtE,oEAAoE;QACpE,6BAA6B;QAC7B,sBAAsB;QACtB,EAAE;QACF,MAAM;QACN,mCAAmC;QACnC,2CAA2C;QAE3C,gEAAgE;QAChE,8DAA8D;QAE9D,iDAAiD;QACjD,IAAI,UAAU,CAAC;QACf,IAAK,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC3C,MAAM,KAAK,OAAO,CAAC,eAAe,CAAC,EAAE;YACrC,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,MAAM,OAAO,GAAG;gBACnD,UAAU;YACZ;QACF;QAEA,4DAA4D;QAC5D,kEAAkE;QAClE,IAAI,MAAM;QACV,IAAI,YAAY,CAAC,GAAG;YAClB,0BAA0B;YAC1B,SAAS,KAAK,WAAW,CAAC;QAC5B,OAAO;YACL,kCAAkC;YAClC,4CAA4C;YAC5C,SAAS,KAAK,WAAW,CAAC,KAAK;QACjC;QAEA,sDAAsD;QACtD,iBAAiB;QACjB,IAAI,WAAW,CAAC,GAAG;YACjB,OAAO,KAAK,KAAK,CAAC,GAAG;YACrB,OAAO,KAAK,KAAK,CAAC,SAAS;YAC3B,IAAI,CAAC,IAAI,GAAG;QACd;QAEA,mEAAmE;QACnE,UAAU,CAAC;QACX,IAAK,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YACxC,MAAM,KAAK,OAAO,CAAC,YAAY,CAAC,EAAE;YAClC,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,MAAM,OAAO,GAAG;gBACnD,UAAU;YACZ;QACF;QACA,gEAAgE;QAChE,IAAI,YAAY,CAAC,GAAG;YAClB,UAAU,KAAK,MAAM;QACvB;QAEA,IAAI,IAAI,CAAC,UAAU,EAAE,KAAK,KAAK;YAAE;QAAW;QAC5C,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG;QACzB,OAAO,KAAK,KAAK,CAAC;QAElB,iBAAiB;QACjB,IAAI,CAAC,SAAS,CAAC;QAEf,4CAA4C;QAC5C,+CAA+C;QAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI;QAEjC,4CAA4C;QAC5C,oCAAoC;QACpC,IAAI,eAAe,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,OACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,KAAK;QAEhD,qBAAqB;QACrB,IAAI,CAAC,cAAc;YACjB,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACpC,IAAK,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;gBAC5C,IAAI,OAAO,SAAS,CAAC,EAAE;gBACvB,IAAI,CAAC,MAAM;oBAAE;gBAAU;gBACvB,IAAI,CAAC,KAAK,KAAK,CAAC,sBAAsB;oBACpC,IAAI,UAAU;oBACd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,GAAG,IAAK;wBAC3C,IAAI,KAAK,UAAU,CAAC,KAAK,KAAK;4BAC5B,yDAAyD;4BACzD,oDAAoD;4BACpD,2CAA2C;4BAC3C,WAAW;wBACb,OAAO;4BACL,WAAW,IAAI,CAAC,EAAE;wBACpB;oBACF;oBACA,qCAAqC;oBACrC,IAAI,CAAC,QAAQ,KAAK,CAAC,sBAAsB;wBACvC,IAAI,aAAa,UAAU,KAAK,CAAC,GAAG;wBACpC,IAAI,UAAU,UAAU,KAAK,CAAC,IAAI;wBAClC,IAAI,MAAM,KAAK,KAAK,CAAC;wBACrB,IAAI,KAAK;4BACP,WAAW,IAAI,CAAC,GAAG,CAAC,EAAE;4BACtB,QAAQ,OAAO,CAAC,GAAG,CAAC,EAAE;wBACxB;wBACA,IAAI,QAAQ,MAAM,EAAE;4BAClB,OAAO,QAAQ,IAAI,CAAC,OAAO;wBAC7B;wBACA,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,CAAC;wBAChC;oBACF;gBACF;YACF;QACF;QAEA,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,gBAAgB;YACzC,IAAI,CAAC,QAAQ,GAAG;QAClB;QAEA,kCAAkC;QAClC,4CAA4C;QAC5C,IAAI,cAAc;YAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;QACjE;IACF;IAEA,gCAAgC;IAChC,IAAI,OAAO,KAAK,OAAO,CAAC;IACxB,IAAI,SAAS,CAAC,GAAG;QACf,yBAAyB;QACzB,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,CAAC;QACxB,OAAO,KAAK,KAAK,CAAC,GAAG;IACvB;IACA,IAAI,KAAK,KAAK,OAAO,CAAC;IACtB,IAAI,OAAO,CAAC,GAAG;QACb,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,CAAC;QAC1B,OAAO,KAAK,KAAK,CAAC,GAAG;IACvB;IACA,IAAI,MAAM;QAAE,IAAI,CAAC,QAAQ,GAAG;IAAM;IAClC,IAAI,eAAe,CAAC,WAAW,IAC3B,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACnC,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,OAAO,IAAI;AACb;AAEA,IAAI,SAAS,CAAC,SAAS,GAAG,SAAS,IAAI;IACrC,IAAI,OAAO,YAAY,IAAI,CAAC;IAC5B,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,EAAE;QACd,IAAI,SAAS,KAAK;YAChB,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,CAAC;QAC1B;QACA,OAAO,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM;IACjD;IACA,IAAI,MAAM;QAAE,IAAI,CAAC,QAAQ,GAAG;IAAM;AACpC;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/mdurl/index.js"], "sourcesContent": ["'use strict';\n\n\nmodule.exports.encode = require('./encode');\nmodule.exports.decode = require('./decode');\nmodule.exports.format = require('./format');\nmodule.exports.parse  = require('./parse');\n"], "names": [], "mappings": "AAAA;AAGA,OAAO,OAAO,CAAC,MAAM;AACrB,OAAO,OAAO,CAAC,MAAM;AACrB,OAAO,OAAO,CAAC,MAAM;AACrB,OAAO,OAAO,CAAC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/uc.micro/properties/Any/regex.js"], "sourcesContent": ["module.exports=/[\\0-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/uc.micro/categories/Cc/regex.js"], "sourcesContent": ["module.exports=/[\\0-\\x1F\\x7F-\\x9F]/"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/uc.micro/categories/Cf/regex.js"], "sourcesContent": ["module.exports=/[\\xAD\\u0600-\\u0605\\u061C\\u06DD\\u070F\\u08E2\\u180E\\u200B-\\u200F\\u202A-\\u202E\\u2060-\\u2064\\u2066-\\u206F\\uFEFF\\uFFF9-\\uFFFB]|\\uD804[\\uDCBD\\uDCCD]|\\uD82F[\\uDCA0-\\uDCA3]|\\uD834[\\uDD73-\\uDD7A]|\\uDB40[\\uDC01\\uDC20-\\uDC7F]/"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/uc.micro/categories/P/regex.js"], "sourcesContent": ["module.exports=/[!-#%-\\*,-\\/:;\\?@\\[-\\]_\\{\\}\\xA1\\xA7\\xAB\\xB6\\xB7\\xBB\\xBF\\u037E\\u0387\\u055A-\\u055F\\u0589\\u058A\\u05BE\\u05C0\\u05C3\\u05C6\\u05F3\\u05F4\\u0609\\u060A\\u060C\\u060D\\u061B\\u061E\\u061F\\u066A-\\u066D\\u06D4\\u0700-\\u070D\\u07F7-\\u07F9\\u0830-\\u083E\\u085E\\u0964\\u0965\\u0970\\u09FD\\u0A76\\u0AF0\\u0C84\\u0DF4\\u0E4F\\u0E5A\\u0E5B\\u0F04-\\u0F12\\u0F14\\u0F3A-\\u0F3D\\u0F85\\u0FD0-\\u0FD4\\u0FD9\\u0FDA\\u104A-\\u104F\\u10FB\\u1360-\\u1368\\u1400\\u166D\\u166E\\u169B\\u169C\\u16EB-\\u16ED\\u1735\\u1736\\u17D4-\\u17D6\\u17D8-\\u17DA\\u1800-\\u180A\\u1944\\u1945\\u1A1E\\u1A1F\\u1AA0-\\u1AA6\\u1AA8-\\u1AAD\\u1B5A-\\u1B60\\u1BFC-\\u1BFF\\u1C3B-\\u1C3F\\u1C7E\\u1C7F\\u1CC0-\\u1CC7\\u1CD3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205E\\u207D\\u207E\\u208D\\u208E\\u2308-\\u230B\\u2329\\u232A\\u2768-\\u2775\\u27C5\\u27C6\\u27E6-\\u27EF\\u2983-\\u2998\\u29D8-\\u29DB\\u29FC\\u29FD\\u2CF9-\\u2CFC\\u2CFE\\u2CFF\\u2D70\\u2E00-\\u2E2E\\u2E30-\\u2E4E\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301F\\u3030\\u303D\\u30A0\\u30FB\\uA4FE\\uA4FF\\uA60D-\\uA60F\\uA673\\uA67E\\uA6F2-\\uA6F7\\uA874-\\uA877\\uA8CE\\uA8CF\\uA8F8-\\uA8FA\\uA8FC\\uA92E\\uA92F\\uA95F\\uA9C1-\\uA9CD\\uA9DE\\uA9DF\\uAA5C-\\uAA5F\\uAADE\\uAADF\\uAAF0\\uAAF1\\uABEB\\uFD3E\\uFD3F\\uFE10-\\uFE19\\uFE30-\\uFE52\\uFE54-\\uFE61\\uFE63\\uFE68\\uFE6A\\uFE6B\\uFF01-\\uFF03\\uFF05-\\uFF0A\\uFF0C-\\uFF0F\\uFF1A\\uFF1B\\uFF1F\\uFF20\\uFF3B-\\uFF3D\\uFF3F\\uFF5B\\uFF5D\\uFF5F-\\uFF65]|\\uD800[\\uDD00-\\uDD02\\uDF9F\\uDFD0]|\\uD801\\uDD6F|\\uD802[\\uDC57\\uDD1F\\uDD3F\\uDE50-\\uDE58\\uDE7F\\uDEF0-\\uDEF6\\uDF39-\\uDF3F\\uDF99-\\uDF9C]|\\uD803[\\uDF55-\\uDF59]|\\uD804[\\uDC47-\\uDC4D\\uDCBB\\uDCBC\\uDCBE-\\uDCC1\\uDD40-\\uDD43\\uDD74\\uDD75\\uDDC5-\\uDDC8\\uDDCD\\uDDDB\\uDDDD-\\uDDDF\\uDE38-\\uDE3D\\uDEA9]|\\uD805[\\uDC4B-\\uDC4F\\uDC5B\\uDC5D\\uDCC6\\uDDC1-\\uDDD7\\uDE41-\\uDE43\\uDE60-\\uDE6C\\uDF3C-\\uDF3E]|\\uD806[\\uDC3B\\uDE3F-\\uDE46\\uDE9A-\\uDE9C\\uDE9E-\\uDEA2]|\\uD807[\\uDC41-\\uDC45\\uDC70\\uDC71\\uDEF7\\uDEF8]|\\uD809[\\uDC70-\\uDC74]|\\uD81A[\\uDE6E\\uDE6F\\uDEF5\\uDF37-\\uDF3B\\uDF44]|\\uD81B[\\uDE97-\\uDE9A]|\\uD82F\\uDC9F|\\uD836[\\uDE87-\\uDE8B]|\\uD83A[\\uDD5E\\uDD5F]/"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/uc.micro/categories/Z/regex.js"], "sourcesContent": ["module.exports=/[ \\xA0\\u1680\\u2000-\\u200A\\u2028\\u2029\\u202F\\u205F\\u3000]/"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/uc.micro/index.js"], "sourcesContent": ["'use strict';\n\nexports.Any = require('./properties/Any/regex');\nexports.Cc  = require('./categories/Cc/regex');\nexports.Cf  = require('./categories/Cf/regex');\nexports.P   = require('./categories/P/regex');\nexports.Z   = require('./categories/Z/regex');\n"], "names": [], "mappings": "AAAA;AAEA,QAAQ,GAAG;AACX,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,QAAQ,CAAC;AACT,QAAQ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/linkify-it/lib/re.js"], "sourcesContent": ["'use strict';\n\n\nmodule.exports = function (opts) {\n  var re = {};\n  opts = opts || {};\n\n  // Use direct extract instead of `regenerate` to reduse browserified size\n  re.src_Any = require('uc.micro/properties/Any/regex').source;\n  re.src_Cc  = require('uc.micro/categories/Cc/regex').source;\n  re.src_Z   = require('uc.micro/categories/Z/regex').source;\n  re.src_P   = require('uc.micro/categories/P/regex').source;\n\n  // \\p{\\Z\\P\\Cc\\CF} (white spaces + control + format + punctuation)\n  re.src_ZPCc = [ re.src_Z, re.src_P, re.src_Cc ].join('|');\n\n  // \\p{\\Z\\Cc} (white spaces + control)\n  re.src_ZCc = [ re.src_Z, re.src_Cc ].join('|');\n\n  // Experimental. List of chars, completely prohibited in links\n  // because can separate it from other part of text\n  var text_separators = '[><\\uff5c]';\n\n  // All possible word characters (everything without punctuation, spaces & controls)\n  // Defined via punctuation & spaces to save space\n  // Should be something like \\p{\\L\\N\\S\\M} (\\w but without `_`)\n  re.src_pseudo_letter       = '(?:(?!' + text_separators + '|' + re.src_ZPCc + ')' + re.src_Any + ')';\n  // The same as abothe but without [0-9]\n  // var src_pseudo_letter_non_d = '(?:(?![0-9]|' + src_ZPCc + ')' + src_Any + ')';\n\n  ////////////////////////////////////////////////////////////////////////////////\n\n  re.src_ip4 =\n\n    '(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)';\n\n  // Prohibit any of \"@/[]()\" in user/pass to avoid wrong domain fetch.\n  re.src_auth    = '(?:(?:(?!' + re.src_ZCc + '|[@/\\\\[\\\\]()]).)+@)?';\n\n  re.src_port =\n\n    '(?::(?:6(?:[0-4]\\\\d{3}|5(?:[0-4]\\\\d{2}|5(?:[0-2]\\\\d|3[0-5])))|[1-5]?\\\\d{1,4}))?';\n\n  re.src_host_terminator =\n\n    '(?=$|' + text_separators + '|' + re.src_ZPCc + ')' +\n    '(?!' + (opts['---'] ? '-(?!--)|' : '-|') + '_|:\\\\d|\\\\.-|\\\\.(?!$|' + re.src_ZPCc + '))';\n\n  re.src_path =\n\n    '(?:' +\n      '[/?#]' +\n        '(?:' +\n          '(?!' + re.src_ZCc + '|' + text_separators + '|[()[\\\\]{}.,\"\\'?!\\\\-;]).|' +\n          '\\\\[(?:(?!' + re.src_ZCc + '|\\\\]).)*\\\\]|' +\n          '\\\\((?:(?!' + re.src_ZCc + '|[)]).)*\\\\)|' +\n          '\\\\{(?:(?!' + re.src_ZCc + '|[}]).)*\\\\}|' +\n          '\\\\\"(?:(?!' + re.src_ZCc + '|[\"]).)+\\\\\"|' +\n          \"\\\\'(?:(?!\" + re.src_ZCc + \"|[']).)+\\\\'|\" +\n          \"\\\\'(?=\" + re.src_pseudo_letter + '|[-])|' +  // allow `I'm_king` if no pair found\n          '\\\\.{2,}[a-zA-Z0-9%/&]|' + // google has many dots in \"google search\" links (#66, #81).\n                                     // github has ... in commit range links,\n                                     // Restrict to\n                                     // - english\n                                     // - percent-encoded\n                                     // - parts of file path\n                                     // - params separator\n                                     // until more examples found.\n          '\\\\.(?!' + re.src_ZCc + '|[.]|$)|' +\n          (opts['---'] ?\n            '\\\\-(?!--(?:[^-]|$))(?:-*)|' // `---` => long dash, terminate\n            :\n            '\\\\-+|'\n          ) +\n          ',(?!' + re.src_ZCc + '|$)|' +       // allow `,,,` in paths\n          ';(?!' + re.src_ZCc + '|$)|' +       // allow `;` if not followed by space-like char\n          '\\\\!+(?!' + re.src_ZCc + '|[!]|$)|' +  // allow `!!!` in paths, but not at the end\n          '\\\\?(?!' + re.src_ZCc + '|[?]|$)' +\n        ')+' +\n      '|\\\\/' +\n    ')?';\n\n  // Allow anything in markdown spec, forbid quote (\") at the first position\n  // because emails enclosed in quotes are far more common\n  re.src_email_name =\n\n    '[\\\\-;:&=\\\\+\\\\$,\\\\.a-zA-Z0-9_][\\\\-;:&=\\\\+\\\\$,\\\\\"\\\\.a-zA-Z0-9_]*';\n\n  re.src_xn =\n\n    'xn--[a-z0-9\\\\-]{1,59}';\n\n  // More to read about domain names\n  // http://serverfault.com/questions/638260/\n\n  re.src_domain_root =\n\n    // Allow letters & digits (http://test1)\n    '(?:' +\n      re.src_xn +\n      '|' +\n      re.src_pseudo_letter + '{1,63}' +\n    ')';\n\n  re.src_domain =\n\n    '(?:' +\n      re.src_xn +\n      '|' +\n      '(?:' + re.src_pseudo_letter + ')' +\n      '|' +\n      '(?:' + re.src_pseudo_letter + '(?:-|' + re.src_pseudo_letter + '){0,61}' + re.src_pseudo_letter + ')' +\n    ')';\n\n  re.src_host =\n\n    '(?:' +\n    // Don't need IP check, because digits are already allowed in normal domain names\n    //   src_ip4 +\n    // '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)*' + re.src_domain/*_root*/ + ')' +\n    ')';\n\n  re.tpl_host_fuzzy =\n\n    '(?:' +\n      re.src_ip4 +\n    '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))' +\n    ')';\n\n  re.tpl_host_no_ip_fuzzy =\n\n    '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))';\n\n  re.src_host_strict =\n\n    re.src_host + re.src_host_terminator;\n\n  re.tpl_host_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_host_terminator;\n\n  re.src_host_port_strict =\n\n    re.src_host + re.src_port + re.src_host_terminator;\n\n  re.tpl_host_port_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_port + re.src_host_terminator;\n\n  re.tpl_host_port_no_ip_fuzzy_strict =\n\n    re.tpl_host_no_ip_fuzzy + re.src_port + re.src_host_terminator;\n\n\n  ////////////////////////////////////////////////////////////////////////////////\n  // Main rules\n\n  // Rude test fuzzy links by host, for quick deny\n  re.tpl_host_fuzzy_test =\n\n    'localhost|www\\\\.|\\\\.\\\\d{1,3}\\\\.|(?:\\\\.(?:%TLDS%)(?:' + re.src_ZPCc + '|>|$))';\n\n  re.tpl_email_fuzzy =\n\n      '(^|' + text_separators + '|\"|\\\\(|' + re.src_ZCc + ')' +\n      '(' + re.src_email_name + '@' + re.tpl_host_fuzzy_strict + ')';\n\n  re.tpl_link_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_fuzzy_strict + re.src_path + ')';\n\n  re.tpl_link_no_ip_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_no_ip_fuzzy_strict + re.src_path + ')';\n\n  return re;\n};\n"], "names": [], "mappings": "AAAA;AAGA,OAAO,OAAO,GAAG,SAAU,IAAI;IAC7B,IAAI,KAAK,CAAC;IACV,OAAO,QAAQ,CAAC;IAEhB,yEAAyE;IACzE,GAAG,OAAO,GAAG,6GAAyC,MAAM;IAC5D,GAAG,MAAM,GAAI,4GAAwC,MAAM;IAC3D,GAAG,KAAK,GAAK,2GAAuC,MAAM;IAC1D,GAAG,KAAK,GAAK,2GAAuC,MAAM;IAE1D,iEAAiE;IACjE,GAAG,QAAQ,GAAG;QAAE,GAAG,KAAK;QAAE,GAAG,KAAK;QAAE,GAAG,MAAM;KAAE,CAAC,IAAI,CAAC;IAErD,qCAAqC;IACrC,GAAG,OAAO,GAAG;QAAE,GAAG,KAAK;QAAE,GAAG,MAAM;KAAE,CAAC,IAAI,CAAC;IAE1C,8DAA8D;IAC9D,kDAAkD;IAClD,IAAI,kBAAkB;IAEtB,mFAAmF;IACnF,iDAAiD;IACjD,6DAA6D;IAC7D,GAAG,iBAAiB,GAAS,WAAW,kBAAkB,MAAM,GAAG,QAAQ,GAAG,MAAM,GAAG,OAAO,GAAG;IACjG,uCAAuC;IACvC,iFAAiF;IAEjF,gFAAgF;IAEhF,GAAG,OAAO,GAER;IAEF,qEAAqE;IACrE,GAAG,QAAQ,GAAM,cAAc,GAAG,OAAO,GAAG;IAE5C,GAAG,QAAQ,GAET;IAEF,GAAG,mBAAmB,GAEpB,UAAU,kBAAkB,MAAM,GAAG,QAAQ,GAAG,MAChD,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,aAAa,IAAI,IAAI,yBAAyB,GAAG,QAAQ,GAAG;IAErF,GAAG,QAAQ,GAET,QACE,UACE,QACE,QAAQ,GAAG,OAAO,GAAG,MAAM,kBAAkB,8BAC7C,cAAc,GAAG,OAAO,GAAG,iBAC3B,cAAc,GAAG,OAAO,GAAG,iBAC3B,cAAc,GAAG,OAAO,GAAG,iBAC3B,cAAc,GAAG,OAAO,GAAG,iBAC3B,cAAc,GAAG,OAAO,GAAG,iBAC3B,WAAW,GAAG,iBAAiB,GAAG,WAAY,oCAAoC;IAClF,2BAA2B,4DAA4D;IAC5D,wCAAwC;IACxC,cAAc;IACd,YAAY;IACZ,oBAAoB;IACpB,uBAAuB;IACvB,qBAAqB;IACrB,6BAA6B;IACxD,WAAW,GAAG,OAAO,GAAG,aACxB,CAAC,IAAI,CAAC,MAAM,GACV,6BAA6B,gCAAgC;OAE7D,OACF,IACA,SAAS,GAAG,OAAO,GAAG,SAAe,uBAAuB;IAC5D,SAAS,GAAG,OAAO,GAAG,SAAe,+CAA+C;IACpF,YAAY,GAAG,OAAO,GAAG,aAAc,2CAA2C;IAClF,WAAW,GAAG,OAAO,GAAG,YAC1B,OACF,SACF;IAEF,0EAA0E;IAC1E,wDAAwD;IACxD,GAAG,cAAc,GAEf;IAEF,GAAG,MAAM,GAEP;IAEF,kCAAkC;IAClC,2CAA2C;IAE3C,GAAG,eAAe,GAEhB,wCAAwC;IACxC,QACE,GAAG,MAAM,GACT,MACA,GAAG,iBAAiB,GAAG,WACzB;IAEF,GAAG,UAAU,GAEX,QACE,GAAG,MAAM,GACT,MACA,QAAQ,GAAG,iBAAiB,GAAG,MAC/B,MACA,QAAQ,GAAG,iBAAiB,GAAG,UAAU,GAAG,iBAAiB,GAAG,YAAY,GAAG,iBAAiB,GAAG,MACrG;IAEF,GAAG,QAAQ,GAET,QACA,iFAAiF;IACjF,cAAc;IACd,QAAQ;IACN,cAAc,GAAG,UAAU,GAAG,WAAW,GAAG,UAAU,CAAA,OAAO,MAAK,MACpE;IAEF,GAAG,cAAc,GAEf,QACE,GAAG,OAAO,GACZ,MACE,cAAc,GAAG,UAAU,GAAG,sBAChC;IAEF,GAAG,oBAAoB,GAErB,cAAc,GAAG,UAAU,GAAG;IAEhC,GAAG,eAAe,GAEhB,GAAG,QAAQ,GAAG,GAAG,mBAAmB;IAEtC,GAAG,qBAAqB,GAEtB,GAAG,cAAc,GAAG,GAAG,mBAAmB;IAE5C,GAAG,oBAAoB,GAErB,GAAG,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,mBAAmB;IAEpD,GAAG,0BAA0B,GAE3B,GAAG,cAAc,GAAG,GAAG,QAAQ,GAAG,GAAG,mBAAmB;IAE1D,GAAG,gCAAgC,GAEjC,GAAG,oBAAoB,GAAG,GAAG,QAAQ,GAAG,GAAG,mBAAmB;IAGhE,gFAAgF;IAChF,aAAa;IAEb,gDAAgD;IAChD,GAAG,mBAAmB,GAEpB,wDAAwD,GAAG,QAAQ,GAAG;IAExE,GAAG,eAAe,GAEd,QAAQ,kBAAkB,YAAY,GAAG,OAAO,GAAG,MACnD,MAAM,GAAG,cAAc,GAAG,MAAM,GAAG,qBAAqB,GAAG;IAE/D,GAAG,cAAc,GACb,gEAAgE;IAChE,6CAA6C;IAC7C,0CAA0C,GAAG,QAAQ,GAAG,OACxD,0BAA0B,GAAG,0BAA0B,GAAG,GAAG,QAAQ,GAAG;IAE5E,GAAG,oBAAoB,GACnB,gEAAgE;IAChE,6CAA6C;IAC7C,0CAA0C,GAAG,QAAQ,GAAG,OACxD,0BAA0B,GAAG,gCAAgC,GAAG,GAAG,QAAQ,GAAG;IAElF,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/linkify-it/index.js"], "sourcesContent": ["'use strict';\n\n\n////////////////////////////////////////////////////////////////////////////////\n// Helpers\n\n// Merge objects\n//\nfunction assign(obj /*from1, from2, from3, ...*/) {\n  var sources = Array.prototype.slice.call(arguments, 1);\n\n  sources.forEach(function (source) {\n    if (!source) { return; }\n\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key];\n    });\n  });\n\n  return obj;\n}\n\nfunction _class(obj) { return Object.prototype.toString.call(obj); }\nfunction isString(obj) { return _class(obj) === '[object String]'; }\nfunction isObject(obj) { return _class(obj) === '[object Object]'; }\nfunction isRegExp(obj) { return _class(obj) === '[object RegExp]'; }\nfunction isFunction(obj) { return _class(obj) === '[object Function]'; }\n\n\nfunction escapeRE(str) { return str.replace(/[.?*+^$[\\]\\\\(){}|-]/g, '\\\\$&'); }\n\n////////////////////////////////////////////////////////////////////////////////\n\n\nvar defaultOptions = {\n  fuzzyLink: true,\n  fuzzyEmail: true,\n  fuzzyIP: false\n};\n\n\nfunction isOptionsObj(obj) {\n  return Object.keys(obj || {}).reduce(function (acc, k) {\n    return acc || defaultOptions.hasOwnProperty(k);\n  }, false);\n}\n\n\nvar defaultSchemas = {\n  'http:': {\n    validate: function (text, pos, self) {\n      var tail = text.slice(pos);\n\n      if (!self.re.http) {\n        // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.http =  new RegExp(\n          '^\\\\/\\\\/' + self.re.src_auth + self.re.src_host_port_strict + self.re.src_path, 'i'\n        );\n      }\n      if (self.re.http.test(tail)) {\n        return tail.match(self.re.http)[0].length;\n      }\n      return 0;\n    }\n  },\n  'https:':  'http:',\n  'ftp:':    'http:',\n  '//':      {\n    validate: function (text, pos, self) {\n      var tail = text.slice(pos);\n\n      if (!self.re.no_http) {\n      // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.no_http =  new RegExp(\n          '^' +\n          self.re.src_auth +\n          // Don't allow single-level domains, because of false positives like '//test'\n          // with code comments\n          '(?:localhost|(?:(?:' + self.re.src_domain + ')\\\\.)+' + self.re.src_domain_root + ')' +\n          self.re.src_port +\n          self.re.src_host_terminator +\n          self.re.src_path,\n\n          'i'\n        );\n      }\n\n      if (self.re.no_http.test(tail)) {\n        // should not be `://` & `///`, that protects from errors in protocol name\n        if (pos >= 3 && text[pos - 3] === ':') { return 0; }\n        if (pos >= 3 && text[pos - 3] === '/') { return 0; }\n        return tail.match(self.re.no_http)[0].length;\n      }\n      return 0;\n    }\n  },\n  'mailto:': {\n    validate: function (text, pos, self) {\n      var tail = text.slice(pos);\n\n      if (!self.re.mailto) {\n        self.re.mailto =  new RegExp(\n          '^' + self.re.src_email_name + '@' + self.re.src_host_strict, 'i'\n        );\n      }\n      if (self.re.mailto.test(tail)) {\n        return tail.match(self.re.mailto)[0].length;\n      }\n      return 0;\n    }\n  }\n};\n\n/*eslint-disable max-len*/\n\n// RE pattern for 2-character tlds (autogenerated by ./support/tlds_2char_gen.js)\nvar tlds_2ch_src_re = 'a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]';\n\n// DON'T try to make PRs with changes. Extend TLDs with LinkifyIt.tlds() instead\nvar tlds_default = 'biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф'.split('|');\n\n/*eslint-enable max-len*/\n\n////////////////////////////////////////////////////////////////////////////////\n\nfunction resetScanCache(self) {\n  self.__index__ = -1;\n  self.__text_cache__   = '';\n}\n\nfunction createValidator(re) {\n  return function (text, pos) {\n    var tail = text.slice(pos);\n\n    if (re.test(tail)) {\n      return tail.match(re)[0].length;\n    }\n    return 0;\n  };\n}\n\nfunction createNormalizer() {\n  return function (match, self) {\n    self.normalize(match);\n  };\n}\n\n// Schemas compiler. Build regexps.\n//\nfunction compile(self) {\n\n  // Load & clone RE patterns.\n  var re = self.re = require('./lib/re')(self.__opts__);\n\n  // Define dynamic patterns\n  var tlds = self.__tlds__.slice();\n\n  self.onCompile();\n\n  if (!self.__tlds_replaced__) {\n    tlds.push(tlds_2ch_src_re);\n  }\n  tlds.push(re.src_xn);\n\n  re.src_tlds = tlds.join('|');\n\n  function untpl(tpl) { return tpl.replace('%TLDS%', re.src_tlds); }\n\n  re.email_fuzzy      = RegExp(untpl(re.tpl_email_fuzzy), 'i');\n  re.link_fuzzy       = RegExp(untpl(re.tpl_link_fuzzy), 'i');\n  re.link_no_ip_fuzzy = RegExp(untpl(re.tpl_link_no_ip_fuzzy), 'i');\n  re.host_fuzzy_test  = RegExp(untpl(re.tpl_host_fuzzy_test), 'i');\n\n  //\n  // Compile each schema\n  //\n\n  var aliases = [];\n\n  self.__compiled__ = {}; // Reset compiled data\n\n  function schemaError(name, val) {\n    throw new Error('(LinkifyIt) Invalid schema \"' + name + '\": ' + val);\n  }\n\n  Object.keys(self.__schemas__).forEach(function (name) {\n    var val = self.__schemas__[name];\n\n    // skip disabled methods\n    if (val === null) { return; }\n\n    var compiled = { validate: null, link: null };\n\n    self.__compiled__[name] = compiled;\n\n    if (isObject(val)) {\n      if (isRegExp(val.validate)) {\n        compiled.validate = createValidator(val.validate);\n      } else if (isFunction(val.validate)) {\n        compiled.validate = val.validate;\n      } else {\n        schemaError(name, val);\n      }\n\n      if (isFunction(val.normalize)) {\n        compiled.normalize = val.normalize;\n      } else if (!val.normalize) {\n        compiled.normalize = createNormalizer();\n      } else {\n        schemaError(name, val);\n      }\n\n      return;\n    }\n\n    if (isString(val)) {\n      aliases.push(name);\n      return;\n    }\n\n    schemaError(name, val);\n  });\n\n  //\n  // Compile postponed aliases\n  //\n\n  aliases.forEach(function (alias) {\n    if (!self.__compiled__[self.__schemas__[alias]]) {\n      // Silently fail on missed schemas to avoid errons on disable.\n      // schemaError(alias, self.__schemas__[alias]);\n      return;\n    }\n\n    self.__compiled__[alias].validate =\n      self.__compiled__[self.__schemas__[alias]].validate;\n    self.__compiled__[alias].normalize =\n      self.__compiled__[self.__schemas__[alias]].normalize;\n  });\n\n  //\n  // Fake record for guessed links\n  //\n  self.__compiled__[''] = { validate: null, normalize: createNormalizer() };\n\n  //\n  // Build schema condition\n  //\n  var slist = Object.keys(self.__compiled__)\n                      .filter(function (name) {\n                        // Filter disabled & fake schemas\n                        return name.length > 0 && self.__compiled__[name];\n                      })\n                      .map(escapeRE)\n                      .join('|');\n  // (?!_) cause 1.5x slowdown\n  self.re.schema_test     = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'i');\n  self.re.schema_search   = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'ig');\n  self.re.schema_at_start = RegExp('^' + self.re.schema_search.source, 'i');\n\n  self.re.pretest = RegExp(\n    '(' + self.re.schema_test.source + ')|(' + self.re.host_fuzzy_test.source + ')|@',\n    'i'\n  );\n\n  //\n  // Cleanup\n  //\n\n  resetScanCache(self);\n}\n\n/**\n * class Match\n *\n * Match result. Single element of array, returned by [[LinkifyIt#match]]\n **/\nfunction Match(self, shift) {\n  var start = self.__index__,\n      end   = self.__last_index__,\n      text  = self.__text_cache__.slice(start, end);\n\n  /**\n   * Match#schema -> String\n   *\n   * Prefix (protocol) for matched string.\n   **/\n  this.schema    = self.__schema__.toLowerCase();\n  /**\n   * Match#index -> Number\n   *\n   * First position of matched string.\n   **/\n  this.index     = start + shift;\n  /**\n   * Match#lastIndex -> Number\n   *\n   * Next position after matched string.\n   **/\n  this.lastIndex = end + shift;\n  /**\n   * Match#raw -> String\n   *\n   * Matched string.\n   **/\n  this.raw       = text;\n  /**\n   * Match#text -> String\n   *\n   * Notmalized text of matched string.\n   **/\n  this.text      = text;\n  /**\n   * Match#url -> String\n   *\n   * Normalized url of matched string.\n   **/\n  this.url       = text;\n}\n\nfunction createMatch(self, shift) {\n  var match = new Match(self, shift);\n\n  self.__compiled__[match.schema].normalize(match, self);\n\n  return match;\n}\n\n\n/**\n * class LinkifyIt\n **/\n\n/**\n * new LinkifyIt(schemas, options)\n * - schemas (Object): Optional. Additional schemas to validate (prefix/validator)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Creates new linkifier instance with optional additional schemas.\n * Can be called without `new` keyword for convenience.\n *\n * By default understands:\n *\n * - `http(s)://...` , `ftp://...`, `mailto:...` & `//...` links\n * - \"fuzzy\" links and emails (example.com, <EMAIL>).\n *\n * `schemas` is an object, where each key/value describes protocol/rule:\n *\n * - __key__ - link prefix (usually, protocol name with `:` at the end, `skype:`\n *   for example). `linkify-it` makes shure that prefix is not preceeded with\n *   alphanumeric char and symbols. Only whitespaces and punctuation allowed.\n * - __value__ - rule to check tail after link prefix\n *   - _String_ - just alias to existing rule\n *   - _Object_\n *     - _validate_ - validator function (should return matched length on success),\n *       or `RegExp`.\n *     - _normalize_ - optional function to normalize text & url of matched result\n *       (for example, for @twitter mentions).\n *\n * `options`:\n *\n * - __fuzzyLink__ - recognige URL-s without `http(s):` prefix. Default `true`.\n * - __fuzzyIP__ - allow IPs in fuzzy links above. Can conflict with some texts\n *   like version numbers. Default `false`.\n * - __fuzzyEmail__ - recognize emails without `mailto:` prefix.\n *\n **/\nfunction LinkifyIt(schemas, options) {\n  if (!(this instanceof LinkifyIt)) {\n    return new LinkifyIt(schemas, options);\n  }\n\n  if (!options) {\n    if (isOptionsObj(schemas)) {\n      options = schemas;\n      schemas = {};\n    }\n  }\n\n  this.__opts__           = assign({}, defaultOptions, options);\n\n  // Cache last tested result. Used to skip repeating steps on next `match` call.\n  this.__index__          = -1;\n  this.__last_index__     = -1; // Next scan position\n  this.__schema__         = '';\n  this.__text_cache__     = '';\n\n  this.__schemas__        = assign({}, defaultSchemas, schemas);\n  this.__compiled__       = {};\n\n  this.__tlds__           = tlds_default;\n  this.__tlds_replaced__  = false;\n\n  this.re = {};\n\n  compile(this);\n}\n\n\n/** chainable\n * LinkifyIt#add(schema, definition)\n * - schema (String): rule name (fixed pattern prefix)\n * - definition (String|RegExp|Object): schema definition\n *\n * Add new rule definition. See constructor description for details.\n **/\nLinkifyIt.prototype.add = function add(schema, definition) {\n  this.__schemas__[schema] = definition;\n  compile(this);\n  return this;\n};\n\n\n/** chainable\n * LinkifyIt#set(options)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Set recognition options for links without schema.\n **/\nLinkifyIt.prototype.set = function set(options) {\n  this.__opts__ = assign(this.__opts__, options);\n  return this;\n};\n\n\n/**\n * LinkifyIt#test(text) -> Boolean\n *\n * Searches linkifiable pattern and returns `true` on success or `false` on fail.\n **/\nLinkifyIt.prototype.test = function test(text) {\n  // Reset scan cache\n  this.__text_cache__ = text;\n  this.__index__      = -1;\n\n  if (!text.length) { return false; }\n\n  var m, ml, me, len, shift, next, re, tld_pos, at_pos;\n\n  // try to scan for link with schema - that's the most simple rule\n  if (this.re.schema_test.test(text)) {\n    re = this.re.schema_search;\n    re.lastIndex = 0;\n    while ((m = re.exec(text)) !== null) {\n      len = this.testSchemaAt(text, m[2], re.lastIndex);\n      if (len) {\n        this.__schema__     = m[2];\n        this.__index__      = m.index + m[1].length;\n        this.__last_index__ = m.index + m[0].length + len;\n        break;\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyLink && this.__compiled__['http:']) {\n    // guess schemaless links\n    tld_pos = text.search(this.re.host_fuzzy_test);\n    if (tld_pos >= 0) {\n      // if tld is located after found link - no need to check fuzzy pattern\n      if (this.__index__ < 0 || tld_pos < this.__index__) {\n        if ((ml = text.match(this.__opts__.fuzzyIP ? this.re.link_fuzzy : this.re.link_no_ip_fuzzy)) !== null) {\n\n          shift = ml.index + ml[1].length;\n\n          if (this.__index__ < 0 || shift < this.__index__) {\n            this.__schema__     = '';\n            this.__index__      = shift;\n            this.__last_index__ = ml.index + ml[0].length;\n          }\n        }\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyEmail && this.__compiled__['mailto:']) {\n    // guess schemaless emails\n    at_pos = text.indexOf('@');\n    if (at_pos >= 0) {\n      // We can't skip this check, because this cases are possible:\n      // <EMAIL>, <EMAIL>\n      if ((me = text.match(this.re.email_fuzzy)) !== null) {\n\n        shift = me.index + me[1].length;\n        next  = me.index + me[0].length;\n\n        if (this.__index__ < 0 || shift < this.__index__ ||\n            (shift === this.__index__ && next > this.__last_index__)) {\n          this.__schema__     = 'mailto:';\n          this.__index__      = shift;\n          this.__last_index__ = next;\n        }\n      }\n    }\n  }\n\n  return this.__index__ >= 0;\n};\n\n\n/**\n * LinkifyIt#pretest(text) -> Boolean\n *\n * Very quick check, that can give false positives. Returns true if link MAY BE\n * can exists. Can be used for speed optimization, when you need to check that\n * link NOT exists.\n **/\nLinkifyIt.prototype.pretest = function pretest(text) {\n  return this.re.pretest.test(text);\n};\n\n\n/**\n * LinkifyIt#testSchemaAt(text, name, position) -> Number\n * - text (String): text to scan\n * - name (String): rule (schema) name\n * - position (Number): text offset to check from\n *\n * Similar to [[LinkifyIt#test]] but checks only specific protocol tail exactly\n * at given position. Returns length of found pattern (0 on fail).\n **/\nLinkifyIt.prototype.testSchemaAt = function testSchemaAt(text, schema, pos) {\n  // If not supported schema check requested - terminate\n  if (!this.__compiled__[schema.toLowerCase()]) {\n    return 0;\n  }\n  return this.__compiled__[schema.toLowerCase()].validate(text, pos, this);\n};\n\n\n/**\n * LinkifyIt#match(text) -> Array|null\n *\n * Returns array of found link descriptions or `null` on fail. We strongly\n * recommend to use [[LinkifyIt#test]] first, for best speed.\n *\n * ##### Result match description\n *\n * - __schema__ - link schema, can be empty for fuzzy links, or `//` for\n *   protocol-neutral  links.\n * - __index__ - offset of matched text\n * - __lastIndex__ - index of next char after mathch end\n * - __raw__ - matched text\n * - __text__ - normalized text\n * - __url__ - link, generated from matched text\n **/\nLinkifyIt.prototype.match = function match(text) {\n  var shift = 0, result = [];\n\n  // Try to take previous element from cache, if .test() called before\n  if (this.__index__ >= 0 && this.__text_cache__ === text) {\n    result.push(createMatch(this, shift));\n    shift = this.__last_index__;\n  }\n\n  // Cut head if cache was used\n  var tail = shift ? text.slice(shift) : text;\n\n  // Scan string until end reached\n  while (this.test(tail)) {\n    result.push(createMatch(this, shift));\n\n    tail = tail.slice(this.__last_index__);\n    shift += this.__last_index__;\n  }\n\n  if (result.length) {\n    return result;\n  }\n\n  return null;\n};\n\n\n/**\n * LinkifyIt#matchAtStart(text) -> Match|null\n *\n * Returns fully-formed (not fuzzy) link if it starts at the beginning\n * of the string, and null otherwise.\n **/\nLinkifyIt.prototype.matchAtStart = function matchAtStart(text) {\n  // Reset scan cache\n  this.__text_cache__ = text;\n  this.__index__      = -1;\n\n  if (!text.length) return null;\n\n  var m = this.re.schema_at_start.exec(text);\n  if (!m) return null;\n\n  var len = this.testSchemaAt(text, m[2], m[0].length);\n  if (!len) return null;\n\n  this.__schema__     = m[2];\n  this.__index__      = m.index + m[1].length;\n  this.__last_index__ = m.index + m[0].length + len;\n\n  return createMatch(this, 0);\n};\n\n\n/** chainable\n * LinkifyIt#tlds(list [, keepOld]) -> this\n * - list (Array): list of tlds\n * - keepOld (Boolean): merge with current list if `true` (`false` by default)\n *\n * Load (or merge) new tlds list. Those are user for fuzzy links (without prefix)\n * to avoid false positives. By default this algorythm used:\n *\n * - hostname with any 2-letter root zones are ok.\n * - biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф\n *   are ok.\n * - encoded (`xn--...`) root zones are ok.\n *\n * If list is replaced, then exact match for 2-chars root zones will be checked.\n **/\nLinkifyIt.prototype.tlds = function tlds(list, keepOld) {\n  list = Array.isArray(list) ? list : [ list ];\n\n  if (!keepOld) {\n    this.__tlds__ = list.slice();\n    this.__tlds_replaced__ = true;\n    compile(this);\n    return this;\n  }\n\n  this.__tlds__ = this.__tlds__.concat(list)\n                                  .sort()\n                                  .filter(function (el, idx, arr) {\n                                    return el !== arr[idx - 1];\n                                  })\n                                  .reverse();\n\n  compile(this);\n  return this;\n};\n\n/**\n * LinkifyIt#normalize(match)\n *\n * Default normalizer (if schema does not define it's own).\n **/\nLinkifyIt.prototype.normalize = function normalize(match) {\n\n  // Do minimal possible changes by default. Need to collect feedback prior\n  // to move forward https://github.com/markdown-it/linkify-it/issues/1\n\n  if (!match.schema) { match.url = 'http://' + match.url; }\n\n  if (match.schema === 'mailto:' && !/^mailto:/i.test(match.url)) {\n    match.url = 'mailto:' + match.url;\n  }\n};\n\n\n/**\n * LinkifyIt#onCompile()\n *\n * Override to modify basic RegExp-s.\n **/\nLinkifyIt.prototype.onCompile = function onCompile() {\n};\n\n\nmodule.exports = LinkifyIt;\n"], "names": [], "mappings": "AAAA;AAGA,gFAAgF;AAChF,UAAU;AAEV,gBAAgB;AAChB,EAAE;AACF,SAAS,OAAO,IAAI,0BAA0B,GAA3B;IACjB,IAAI,UAAU,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IAEpD,QAAQ,OAAO,CAAC,SAAU,MAAM;QAC9B,IAAI,CAAC,QAAQ;YAAE;QAAQ;QAEvB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAU,GAAG;YACvC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QACxB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,OAAO,GAAG;IAAI,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;AAAM;AACnE,SAAS,SAAS,GAAG;IAAI,OAAO,OAAO,SAAS;AAAmB;AACnE,SAAS,SAAS,GAAG;IAAI,OAAO,OAAO,SAAS;AAAmB;AACnE,SAAS,SAAS,GAAG;IAAI,OAAO,OAAO,SAAS;AAAmB;AACnE,SAAS,WAAW,GAAG;IAAI,OAAO,OAAO,SAAS;AAAqB;AAGvE,SAAS,SAAS,GAAG;IAAI,OAAO,IAAI,OAAO,CAAC,wBAAwB;AAAS;AAE7E,gFAAgF;AAGhF,IAAI,iBAAiB;IACnB,WAAW;IACX,YAAY;IACZ,SAAS;AACX;AAGA,SAAS,aAAa,GAAG;IACvB,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC;QACnD,OAAO,OAAO,eAAe,cAAc,CAAC;IAC9C,GAAG;AACL;AAGA,IAAI,iBAAiB;IACnB,SAAS;QACP,UAAU,SAAU,IAAI,EAAE,GAAG,EAAE,IAAI;YACjC,IAAI,OAAO,KAAK,KAAK,CAAC;YAEtB,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE;gBACjB,iFAAiF;gBACjF,KAAK,EAAE,CAAC,IAAI,GAAI,IAAI,OAClB,YAAY,KAAK,EAAE,CAAC,QAAQ,GAAG,KAAK,EAAE,CAAC,oBAAoB,GAAG,KAAK,EAAE,CAAC,QAAQ,EAAE;YAEpF;YACA,IAAI,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;gBAC3B,OAAO,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM;YAC3C;YACA,OAAO;QACT;IACF;IACA,UAAW;IACX,QAAW;IACX,MAAW;QACT,UAAU,SAAU,IAAI,EAAE,GAAG,EAAE,IAAI;YACjC,IAAI,OAAO,KAAK,KAAK,CAAC;YAEtB,IAAI,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE;gBACtB,iFAAiF;gBAC/E,KAAK,EAAE,CAAC,OAAO,GAAI,IAAI,OACrB,MACA,KAAK,EAAE,CAAC,QAAQ,GAChB,6EAA6E;gBAC7E,qBAAqB;gBACrB,wBAAwB,KAAK,EAAE,CAAC,UAAU,GAAG,WAAW,KAAK,EAAE,CAAC,eAAe,GAAG,MAClF,KAAK,EAAE,CAAC,QAAQ,GAChB,KAAK,EAAE,CAAC,mBAAmB,GAC3B,KAAK,EAAE,CAAC,QAAQ,EAEhB;YAEJ;YAEA,IAAI,KAAK,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO;gBAC9B,0EAA0E;gBAC1E,IAAI,OAAO,KAAK,IAAI,CAAC,MAAM,EAAE,KAAK,KAAK;oBAAE,OAAO;gBAAG;gBACnD,IAAI,OAAO,KAAK,IAAI,CAAC,MAAM,EAAE,KAAK,KAAK;oBAAE,OAAO;gBAAG;gBACnD,OAAO,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM;YAC9C;YACA,OAAO;QACT;IACF;IACA,WAAW;QACT,UAAU,SAAU,IAAI,EAAE,GAAG,EAAE,IAAI;YACjC,IAAI,OAAO,KAAK,KAAK,CAAC;YAEtB,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE;gBACnB,KAAK,EAAE,CAAC,MAAM,GAAI,IAAI,OACpB,MAAM,KAAK,EAAE,CAAC,cAAc,GAAG,MAAM,KAAK,EAAE,CAAC,eAAe,EAAE;YAElE;YACA,IAAI,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;gBAC7B,OAAO,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM;YAC7C;YACA,OAAO;QACT;IACF;AACF;AAEA,wBAAwB,GAExB,iFAAiF;AACjF,IAAI,kBAAkB;AAEtB,gFAAgF;AAChF,IAAI,eAAe,8EAA8E,KAAK,CAAC;AAEvG,uBAAuB,GAEvB,gFAAgF;AAEhF,SAAS,eAAe,IAAI;IAC1B,KAAK,SAAS,GAAG,CAAC;IAClB,KAAK,cAAc,GAAK;AAC1B;AAEA,SAAS,gBAAgB,EAAE;IACzB,OAAO,SAAU,IAAI,EAAE,GAAG;QACxB,IAAI,OAAO,KAAK,KAAK,CAAC;QAEtB,IAAI,GAAG,IAAI,CAAC,OAAO;YACjB,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM;QACjC;QACA,OAAO;IACT;AACF;AAEA,SAAS;IACP,OAAO,SAAU,KAAK,EAAE,IAAI;QAC1B,KAAK,SAAS,CAAC;IACjB;AACF;AAEA,mCAAmC;AACnC,EAAE;AACF,SAAS,QAAQ,IAAI;IAEnB,4BAA4B;IAC5B,IAAI,KAAK,KAAK,EAAE,GAAG,iGAAoB,KAAK,QAAQ;IAEpD,0BAA0B;IAC1B,IAAI,OAAO,KAAK,QAAQ,CAAC,KAAK;IAE9B,KAAK,SAAS;IAEd,IAAI,CAAC,KAAK,iBAAiB,EAAE;QAC3B,KAAK,IAAI,CAAC;IACZ;IACA,KAAK,IAAI,CAAC,GAAG,MAAM;IAEnB,GAAG,QAAQ,GAAG,KAAK,IAAI,CAAC;IAExB,SAAS,MAAM,GAAG;QAAI,OAAO,IAAI,OAAO,CAAC,UAAU,GAAG,QAAQ;IAAG;IAEjE,GAAG,WAAW,GAAQ,OAAO,MAAM,GAAG,eAAe,GAAG;IACxD,GAAG,UAAU,GAAS,OAAO,MAAM,GAAG,cAAc,GAAG;IACvD,GAAG,gBAAgB,GAAG,OAAO,MAAM,GAAG,oBAAoB,GAAG;IAC7D,GAAG,eAAe,GAAI,OAAO,MAAM,GAAG,mBAAmB,GAAG;IAE5D,EAAE;IACF,sBAAsB;IACtB,EAAE;IAEF,IAAI,UAAU,EAAE;IAEhB,KAAK,YAAY,GAAG,CAAC,GAAG,sBAAsB;IAE9C,SAAS,YAAY,IAAI,EAAE,GAAG;QAC5B,MAAM,IAAI,MAAM,iCAAiC,OAAO,QAAQ;IAClE;IAEA,OAAO,IAAI,CAAC,KAAK,WAAW,EAAE,OAAO,CAAC,SAAU,IAAI;QAClD,IAAI,MAAM,KAAK,WAAW,CAAC,KAAK;QAEhC,wBAAwB;QACxB,IAAI,QAAQ,MAAM;YAAE;QAAQ;QAE5B,IAAI,WAAW;YAAE,UAAU;YAAM,MAAM;QAAK;QAE5C,KAAK,YAAY,CAAC,KAAK,GAAG;QAE1B,IAAI,SAAS,MAAM;YACjB,IAAI,SAAS,IAAI,QAAQ,GAAG;gBAC1B,SAAS,QAAQ,GAAG,gBAAgB,IAAI,QAAQ;YAClD,OAAO,IAAI,WAAW,IAAI,QAAQ,GAAG;gBACnC,SAAS,QAAQ,GAAG,IAAI,QAAQ;YAClC,OAAO;gBACL,YAAY,MAAM;YACpB;YAEA,IAAI,WAAW,IAAI,SAAS,GAAG;gBAC7B,SAAS,SAAS,GAAG,IAAI,SAAS;YACpC,OAAO,IAAI,CAAC,IAAI,SAAS,EAAE;gBACzB,SAAS,SAAS,GAAG;YACvB,OAAO;gBACL,YAAY,MAAM;YACpB;YAEA;QACF;QAEA,IAAI,SAAS,MAAM;YACjB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,YAAY,MAAM;IACpB;IAEA,EAAE;IACF,4BAA4B;IAC5B,EAAE;IAEF,QAAQ,OAAO,CAAC,SAAU,KAAK;QAC7B,IAAI,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW,CAAC,MAAM,CAAC,EAAE;YAC/C,8DAA8D;YAC9D,+CAA+C;YAC/C;QACF;QAEA,KAAK,YAAY,CAAC,MAAM,CAAC,QAAQ,GAC/B,KAAK,YAAY,CAAC,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,QAAQ;QACrD,KAAK,YAAY,CAAC,MAAM,CAAC,SAAS,GAChC,KAAK,YAAY,CAAC,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS;IACxD;IAEA,EAAE;IACF,gCAAgC;IAChC,EAAE;IACF,KAAK,YAAY,CAAC,GAAG,GAAG;QAAE,UAAU;QAAM,WAAW;IAAmB;IAExE,EAAE;IACF,yBAAyB;IACzB,EAAE;IACF,IAAI,QAAQ,OAAO,IAAI,CAAC,KAAK,YAAY,EACpB,MAAM,CAAC,SAAU,IAAI;QACpB,iCAAiC;QACjC,OAAO,KAAK,MAAM,GAAG,KAAK,KAAK,YAAY,CAAC,KAAK;IACnD,GACC,GAAG,CAAC,UACJ,IAAI,CAAC;IAC1B,4BAA4B;IAC5B,KAAK,EAAE,CAAC,WAAW,GAAO,OAAO,2BAA2B,GAAG,QAAQ,GAAG,QAAQ,QAAQ,KAAK;IAC/F,KAAK,EAAE,CAAC,aAAa,GAAK,OAAO,2BAA2B,GAAG,QAAQ,GAAG,QAAQ,QAAQ,KAAK;IAC/F,KAAK,EAAE,CAAC,eAAe,GAAG,OAAO,MAAM,KAAK,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE;IAErE,KAAK,EAAE,CAAC,OAAO,GAAG,OAChB,MAAM,KAAK,EAAE,CAAC,WAAW,CAAC,MAAM,GAAG,QAAQ,KAAK,EAAE,CAAC,eAAe,CAAC,MAAM,GAAG,OAC5E;IAGF,EAAE;IACF,UAAU;IACV,EAAE;IAEF,eAAe;AACjB;AAEA;;;;EAIE,GACF,SAAS,MAAM,IAAI,EAAE,KAAK;IACxB,IAAI,QAAQ,KAAK,SAAS,EACtB,MAAQ,KAAK,cAAc,EAC3B,OAAQ,KAAK,cAAc,CAAC,KAAK,CAAC,OAAO;IAE7C;;;;IAIE,GACF,IAAI,CAAC,MAAM,GAAM,KAAK,UAAU,CAAC,WAAW;IAC5C;;;;IAIE,GACF,IAAI,CAAC,KAAK,GAAO,QAAQ;IACzB;;;;IAIE,GACF,IAAI,CAAC,SAAS,GAAG,MAAM;IACvB;;;;IAIE,GACF,IAAI,CAAC,GAAG,GAAS;IACjB;;;;IAIE,GACF,IAAI,CAAC,IAAI,GAAQ;IACjB;;;;IAIE,GACF,IAAI,CAAC,GAAG,GAAS;AACnB;AAEA,SAAS,YAAY,IAAI,EAAE,KAAK;IAC9B,IAAI,QAAQ,IAAI,MAAM,MAAM;IAE5B,KAAK,YAAY,CAAC,MAAM,MAAM,CAAC,CAAC,SAAS,CAAC,OAAO;IAEjD,OAAO;AACT;AAGA;;EAEE,GAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiCE,GACF,SAAS,UAAU,OAAO,EAAE,OAAO;IACjC,IAAI,CAAC,CAAC,IAAI,YAAY,SAAS,GAAG;QAChC,OAAO,IAAI,UAAU,SAAS;IAChC;IAEA,IAAI,CAAC,SAAS;QACZ,IAAI,aAAa,UAAU;YACzB,UAAU;YACV,UAAU,CAAC;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,GAAa,OAAO,CAAC,GAAG,gBAAgB;IAErD,+EAA+E;IAC/E,IAAI,CAAC,SAAS,GAAY,CAAC;IAC3B,IAAI,CAAC,cAAc,GAAO,CAAC,GAAG,qBAAqB;IACnD,IAAI,CAAC,UAAU,GAAW;IAC1B,IAAI,CAAC,cAAc,GAAO;IAE1B,IAAI,CAAC,WAAW,GAAU,OAAO,CAAC,GAAG,gBAAgB;IACrD,IAAI,CAAC,YAAY,GAAS,CAAC;IAE3B,IAAI,CAAC,QAAQ,GAAa;IAC1B,IAAI,CAAC,iBAAiB,GAAI;IAE1B,IAAI,CAAC,EAAE,GAAG,CAAC;IAEX,QAAQ,IAAI;AACd;AAGA;;;;;;EAME,GACF,UAAU,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,MAAM,EAAE,UAAU;IACvD,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG;IAC3B,QAAQ,IAAI;IACZ,OAAO,IAAI;AACb;AAGA;;;;;EAKE,GACF,UAAU,SAAS,CAAC,GAAG,GAAG,SAAS,IAAI,OAAO;IAC5C,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,CAAC,QAAQ,EAAE;IACtC,OAAO,IAAI;AACb;AAGA;;;;EAIE,GACF,UAAU,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,IAAI;IAC3C,mBAAmB;IACnB,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,SAAS,GAAQ,CAAC;IAEvB,IAAI,CAAC,KAAK,MAAM,EAAE;QAAE,OAAO;IAAO;IAElC,IAAI,GAAG,IAAI,IAAI,KAAK,OAAO,MAAM,IAAI,SAAS;IAE9C,iEAAiE;IACjE,IAAI,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO;QAClC,KAAK,IAAI,CAAC,EAAE,CAAC,aAAa;QAC1B,GAAG,SAAS,GAAG;QACf,MAAO,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,MAAM,KAAM;YACnC,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,GAAG,SAAS;YAChD,IAAI,KAAK;gBACP,IAAI,CAAC,UAAU,GAAO,CAAC,CAAC,EAAE;gBAC1B,IAAI,CAAC,SAAS,GAAQ,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM;gBAC3C,IAAI,CAAC,cAAc,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG;gBAC9C;YACF;QACF;IACF;IAEA,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;QACzD,yBAAyB;QACzB,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,eAAe;QAC7C,IAAI,WAAW,GAAG;YAChB,sEAAsE;YACtE,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,UAAU,IAAI,CAAC,SAAS,EAAE;gBAClD,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,MAAM,MAAM;oBAErG,QAAQ,GAAG,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM;oBAE/B,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,QAAQ,IAAI,CAAC,SAAS,EAAE;wBAChD,IAAI,CAAC,UAAU,GAAO;wBACtB,IAAI,CAAC,SAAS,GAAQ;wBACtB,IAAI,CAAC,cAAc,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM;oBAC/C;gBACF;YACF;QACF;IACF;IAEA,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;QAC5D,0BAA0B;QAC1B,SAAS,KAAK,OAAO,CAAC;QACtB,IAAI,UAAU,GAAG;YACf,6DAA6D;YAC7D,2CAA2C;YAC3C,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,MAAM;gBAEnD,QAAQ,GAAG,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM;gBAC/B,OAAQ,GAAG,KAAK,GAAG,EAAE,CAAC,EAAE,CAAC,MAAM;gBAE/B,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,QAAQ,IAAI,CAAC,SAAS,IAC3C,UAAU,IAAI,CAAC,SAAS,IAAI,OAAO,IAAI,CAAC,cAAc,EAAG;oBAC5D,IAAI,CAAC,UAAU,GAAO;oBACtB,IAAI,CAAC,SAAS,GAAQ;oBACtB,IAAI,CAAC,cAAc,GAAG;gBACxB;YACF;QACF;IACF;IAEA,OAAO,IAAI,CAAC,SAAS,IAAI;AAC3B;AAGA;;;;;;EAME,GACF,UAAU,SAAS,CAAC,OAAO,GAAG,SAAS,QAAQ,IAAI;IACjD,OAAO,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;AAC9B;AAGA;;;;;;;;EAQE,GACF,UAAU,SAAS,CAAC,YAAY,GAAG,SAAS,aAAa,IAAI,EAAE,MAAM,EAAE,GAAG;IACxE,sDAAsD;IACtD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,WAAW,GAAG,EAAE;QAC5C,OAAO;IACT;IACA,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,WAAW,GAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI;AACzE;AAGA;;;;;;;;;;;;;;;EAeE,GACF,UAAU,SAAS,CAAC,KAAK,GAAG,SAAS,MAAM,IAAI;IAC7C,IAAI,QAAQ,GAAG,SAAS,EAAE;IAE1B,oEAAoE;IACpE,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK,IAAI,CAAC,cAAc,KAAK,MAAM;QACvD,OAAO,IAAI,CAAC,YAAY,IAAI,EAAE;QAC9B,QAAQ,IAAI,CAAC,cAAc;IAC7B;IAEA,6BAA6B;IAC7B,IAAI,OAAO,QAAQ,KAAK,KAAK,CAAC,SAAS;IAEvC,gCAAgC;IAChC,MAAO,IAAI,CAAC,IAAI,CAAC,MAAO;QACtB,OAAO,IAAI,CAAC,YAAY,IAAI,EAAE;QAE9B,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,cAAc;QACrC,SAAS,IAAI,CAAC,cAAc;IAC9B;IAEA,IAAI,OAAO,MAAM,EAAE;QACjB,OAAO;IACT;IAEA,OAAO;AACT;AAGA;;;;;EAKE,GACF,UAAU,SAAS,CAAC,YAAY,GAAG,SAAS,aAAa,IAAI;IAC3D,mBAAmB;IACnB,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,SAAS,GAAQ,CAAC;IAEvB,IAAI,CAAC,KAAK,MAAM,EAAE,OAAO;IAEzB,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;IACrC,IAAI,CAAC,GAAG,OAAO;IAEf,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM;IACnD,IAAI,CAAC,KAAK,OAAO;IAEjB,IAAI,CAAC,UAAU,GAAO,CAAC,CAAC,EAAE;IAC1B,IAAI,CAAC,SAAS,GAAQ,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM;IAC3C,IAAI,CAAC,cAAc,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG;IAE9C,OAAO,YAAY,IAAI,EAAE;AAC3B;AAGA;;;;;;;;;;;;;;EAcE,GACF,UAAU,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,IAAI,EAAE,OAAO;IACpD,OAAO,MAAM,OAAO,CAAC,QAAQ,OAAO;QAAE;KAAM;IAE5C,IAAI,CAAC,SAAS;QACZ,IAAI,CAAC,QAAQ,GAAG,KAAK,KAAK;QAC1B,IAAI,CAAC,iBAAiB,GAAG;QACzB,QAAQ,IAAI;QACZ,OAAO,IAAI;IACb;IAEA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MACJ,IAAI,GACJ,MAAM,CAAC,SAAU,EAAE,EAAE,GAAG,EAAE,GAAG;QAC5B,OAAO,OAAO,GAAG,CAAC,MAAM,EAAE;IAC5B,GACC,OAAO;IAExC,QAAQ,IAAI;IACZ,OAAO,IAAI;AACb;AAEA;;;;EAIE,GACF,UAAU,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,KAAK;IAEtD,yEAAyE;IACzE,qEAAqE;IAErE,IAAI,CAAC,MAAM,MAAM,EAAE;QAAE,MAAM,GAAG,GAAG,YAAY,MAAM,GAAG;IAAE;IAExD,IAAI,MAAM,MAAM,KAAK,aAAa,CAAC,YAAY,IAAI,CAAC,MAAM,GAAG,GAAG;QAC9D,MAAM,GAAG,GAAG,YAAY,MAAM,GAAG;IACnC;AACF;AAGA;;;;EAIE,GACF,UAAU,SAAS,CAAC,SAAS,GAAG,SAAS,aACzC;AAGA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/node_modules/punycode/punycode.es6.js"], "sourcesContent": ["'use strict';\n\n/** Highest positive signed 32-bit float value */\nconst maxInt = 2147483647; // aka. 0x7FFFFFFF or 2^31-1\n\n/** Bootstring parameters */\nconst base = 36;\nconst tMin = 1;\nconst tMax = 26;\nconst skew = 38;\nconst damp = 700;\nconst initialBias = 72;\nconst initialN = 128; // 0x80\nconst delimiter = '-'; // '\\x2D'\n\n/** Regular expressions */\nconst regexPunycode = /^xn--/;\nconst regexNonASCII = /[^\\0-\\x7F]/; // Note: U+007F DEL is excluded too.\nconst regexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g; // RFC 3490 separators\n\n/** Error messages */\nconst errors = {\n\t'overflow': 'Overflow: input needs wider integers to process',\n\t'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n\t'invalid-input': 'Invalid input'\n};\n\n/** Convenience shortcuts */\nconst baseMinusTMin = base - tMin;\nconst floor = Math.floor;\nconst stringFromCharCode = String.fromCharCode;\n\n/*--------------------------------------------------------------------------*/\n\n/**\n * A generic error utility function.\n * @private\n * @param {String} type The error type.\n * @returns {Error} Throws a `RangeError` with the applicable error message.\n */\nfunction error(type) {\n\tthrow new RangeError(errors[type]);\n}\n\n/**\n * A generic `Array#map` utility function.\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} callback The function that gets called for every array\n * item.\n * @returns {Array} A new array of values returned by the callback function.\n */\nfunction map(array, callback) {\n\tconst result = [];\n\tlet length = array.length;\n\twhile (length--) {\n\t\tresult[length] = callback(array[length]);\n\t}\n\treturn result;\n}\n\n/**\n * A simple `Array#map`-like wrapper to work with domain name strings or email\n * addresses.\n * @private\n * @param {String} domain The domain name or email address.\n * @param {Function} callback The function that gets called for every\n * character.\n * @returns {String} A new string of characters returned by the callback\n * function.\n */\nfunction mapDomain(domain, callback) {\n\tconst parts = domain.split('@');\n\tlet result = '';\n\tif (parts.length > 1) {\n\t\t// In email addresses, only the domain name should be punycoded. Leave\n\t\t// the local part (i.e. everything up to `@`) intact.\n\t\tresult = parts[0] + '@';\n\t\tdomain = parts[1];\n\t}\n\t// Avoid `split(regex)` for IE8 compatibility. See #17.\n\tdomain = domain.replace(regexSeparators, '\\x2E');\n\tconst labels = domain.split('.');\n\tconst encoded = map(labels, callback).join('.');\n\treturn result + encoded;\n}\n\n/**\n * Creates an array containing the numeric code points of each Unicode\n * character in the string. While JavaScript uses UCS-2 internally,\n * this function will convert a pair of surrogate halves (each of which\n * UCS-2 exposes as separate characters) into a single code point,\n * matching UTF-16.\n * @see `punycode.ucs2.encode`\n * @see <https://mathiasbynens.be/notes/javascript-encoding>\n * @memberOf punycode.ucs2\n * @name decode\n * @param {String} string The Unicode input string (UCS-2).\n * @returns {Array} The new array of code points.\n */\nfunction ucs2decode(string) {\n\tconst output = [];\n\tlet counter = 0;\n\tconst length = string.length;\n\twhile (counter < length) {\n\t\tconst value = string.charCodeAt(counter++);\n\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t// It's a high surrogate, and there is a next character.\n\t\t\tconst extra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 0xFC00) == 0xDC00) { // Low surrogate.\n\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t} else {\n\t\t\t\t// It's an unmatched surrogate; only append this code unit, in case the\n\t\t\t\t// next code unit is the high surrogate of a surrogate pair.\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n\n/**\n * Creates a string based on an array of numeric code points.\n * @see `punycode.ucs2.decode`\n * @memberOf punycode.ucs2\n * @name encode\n * @param {Array} codePoints The array of numeric code points.\n * @returns {String} The new Unicode string (UCS-2).\n */\nconst ucs2encode = codePoints => String.fromCodePoint(...codePoints);\n\n/**\n * Converts a basic code point into a digit/integer.\n * @see `digitToBasic()`\n * @private\n * @param {Number} codePoint The basic numeric code point value.\n * @returns {Number} The numeric value of a basic code point (for use in\n * representing integers) in the range `0` to `base - 1`, or `base` if\n * the code point does not represent a value.\n */\nconst basicToDigit = function(codePoint) {\n\tif (codePoint >= 0x30 && codePoint < 0x3A) {\n\t\treturn 26 + (codePoint - 0x30);\n\t}\n\tif (codePoint >= 0x41 && codePoint < 0x5B) {\n\t\treturn codePoint - 0x41;\n\t}\n\tif (codePoint >= 0x61 && codePoint < 0x7B) {\n\t\treturn codePoint - 0x61;\n\t}\n\treturn base;\n};\n\n/**\n * Converts a digit/integer into a basic code point.\n * @see `basicToDigit()`\n * @private\n * @param {Number} digit The numeric value of a basic code point.\n * @returns {Number} The basic code point whose value (when used for\n * representing integers) is `digit`, which needs to be in the range\n * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n * used; else, the lowercase form is used. The behavior is undefined\n * if `flag` is non-zero and `digit` has no uppercase form.\n */\nconst digitToBasic = function(digit, flag) {\n\t//  0..25 map to ASCII a..z or A..Z\n\t// 26..35 map to ASCII 0..9\n\treturn digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n};\n\n/**\n * Bias adaptation function as per section 3.4 of RFC 3492.\n * https://tools.ietf.org/html/rfc3492#section-3.4\n * @private\n */\nconst adapt = function(delta, numPoints, firstTime) {\n\tlet k = 0;\n\tdelta = firstTime ? floor(delta / damp) : delta >> 1;\n\tdelta += floor(delta / numPoints);\n\tfor (/* no initialization */; delta > baseMinusTMin * tMax >> 1; k += base) {\n\t\tdelta = floor(delta / baseMinusTMin);\n\t}\n\treturn floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n};\n\n/**\n * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n * symbols.\n * @memberOf punycode\n * @param {String} input The Punycode string of ASCII-only symbols.\n * @returns {String} The resulting string of Unicode symbols.\n */\nconst decode = function(input) {\n\t// Don't use UCS-2.\n\tconst output = [];\n\tconst inputLength = input.length;\n\tlet i = 0;\n\tlet n = initialN;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points: let `basic` be the number of input code\n\t// points before the last delimiter, or `0` if there is none, then copy\n\t// the first basic code points to the output.\n\n\tlet basic = input.lastIndexOf(delimiter);\n\tif (basic < 0) {\n\t\tbasic = 0;\n\t}\n\n\tfor (let j = 0; j < basic; ++j) {\n\t\t// if it's not a basic code point\n\t\tif (input.charCodeAt(j) >= 0x80) {\n\t\t\terror('not-basic');\n\t\t}\n\t\toutput.push(input.charCodeAt(j));\n\t}\n\n\t// Main decoding loop: start just after the last delimiter if any basic code\n\t// points were copied; start at the beginning otherwise.\n\n\tfor (let index = basic > 0 ? basic + 1 : 0; index < inputLength; /* no final expression */) {\n\n\t\t// `index` is the index of the next character to be consumed.\n\t\t// Decode a generalized variable-length integer into `delta`,\n\t\t// which gets added to `i`. The overflow checking is easier\n\t\t// if we increase `i` as we go, then subtract off its starting\n\t\t// value at the end to obtain `delta`.\n\t\tconst oldi = i;\n\t\tfor (let w = 1, k = base; /* no condition */; k += base) {\n\n\t\t\tif (index >= inputLength) {\n\t\t\t\terror('invalid-input');\n\t\t\t}\n\n\t\t\tconst digit = basicToDigit(input.charCodeAt(index++));\n\n\t\t\tif (digit >= base) {\n\t\t\t\terror('invalid-input');\n\t\t\t}\n\t\t\tif (digit > floor((maxInt - i) / w)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\ti += digit * w;\n\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\n\t\t\tif (digit < t) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tconst baseMinusT = base - t;\n\t\t\tif (w > floor(maxInt / baseMinusT)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tw *= baseMinusT;\n\n\t\t}\n\n\t\tconst out = output.length + 1;\n\t\tbias = adapt(i - oldi, out, oldi == 0);\n\n\t\t// `i` was supposed to wrap around from `out` to `0`,\n\t\t// incrementing `n` each time, so we'll fix that now:\n\t\tif (floor(i / out) > maxInt - n) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tn += floor(i / out);\n\t\ti %= out;\n\n\t\t// Insert `n` at position `i` of the output.\n\t\toutput.splice(i++, 0, n);\n\n\t}\n\n\treturn String.fromCodePoint(...output);\n};\n\n/**\n * Converts a string of Unicode symbols (e.g. a domain name label) to a\n * Punycode string of ASCII-only symbols.\n * @memberOf punycode\n * @param {String} input The string of Unicode symbols.\n * @returns {String} The resulting Punycode string of ASCII-only symbols.\n */\nconst encode = function(input) {\n\tconst output = [];\n\n\t// Convert the input in UCS-2 to an array of Unicode code points.\n\tinput = ucs2decode(input);\n\n\t// Cache the length.\n\tconst inputLength = input.length;\n\n\t// Initialize the state.\n\tlet n = initialN;\n\tlet delta = 0;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points.\n\tfor (const currentValue of input) {\n\t\tif (currentValue < 0x80) {\n\t\t\toutput.push(stringFromCharCode(currentValue));\n\t\t}\n\t}\n\n\tconst basicLength = output.length;\n\tlet handledCPCount = basicLength;\n\n\t// `handledCPCount` is the number of code points that have been handled;\n\t// `basicLength` is the number of basic code points.\n\n\t// Finish the basic string with a delimiter unless it's empty.\n\tif (basicLength) {\n\t\toutput.push(delimiter);\n\t}\n\n\t// Main encoding loop:\n\twhile (handledCPCount < inputLength) {\n\n\t\t// All non-basic code points < n have been handled already. Find the next\n\t\t// larger one:\n\t\tlet m = maxInt;\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue >= n && currentValue < m) {\n\t\t\t\tm = currentValue;\n\t\t\t}\n\t\t}\n\n\t\t// Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n\t\t// but guard against overflow.\n\t\tconst handledCPCountPlusOne = handledCPCount + 1;\n\t\tif (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tdelta += (m - n) * handledCPCountPlusOne;\n\t\tn = m;\n\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue < n && ++delta > maxInt) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\t\t\tif (currentValue === n) {\n\t\t\t\t// Represent delta as a generalized variable-length integer.\n\t\t\t\tlet q = delta;\n\t\t\t\tfor (let k = base; /* no condition */; k += base) {\n\t\t\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\t\t\t\t\tif (q < t) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tconst qMinusT = q - t;\n\t\t\t\t\tconst baseMinusT = base - t;\n\t\t\t\t\toutput.push(\n\t\t\t\t\t\tstringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0))\n\t\t\t\t\t);\n\t\t\t\t\tq = floor(qMinusT / baseMinusT);\n\t\t\t\t}\n\n\t\t\t\toutput.push(stringFromCharCode(digitToBasic(q, 0)));\n\t\t\t\tbias = adapt(delta, handledCPCountPlusOne, handledCPCount === basicLength);\n\t\t\t\tdelta = 0;\n\t\t\t\t++handledCPCount;\n\t\t\t}\n\t\t}\n\n\t\t++delta;\n\t\t++n;\n\n\t}\n\treturn output.join('');\n};\n\n/**\n * Converts a Punycode string representing a domain name or an email address\n * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n * it doesn't matter if you call it on a string that has already been\n * converted to Unicode.\n * @memberOf punycode\n * @param {String} input The Punycoded domain name or email address to\n * convert to Unicode.\n * @returns {String} The Unicode representation of the given Punycode\n * string.\n */\nconst toUnicode = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexPunycode.test(string)\n\t\t\t? decode(string.slice(4).toLowerCase())\n\t\t\t: string;\n\t});\n};\n\n/**\n * Converts a Unicode string representing a domain name or an email address to\n * Punycode. Only the non-ASCII parts of the domain name will be converted,\n * i.e. it doesn't matter if you call it with a domain that's already in\n * ASCII.\n * @memberOf punycode\n * @param {String} input The domain name or email address to convert, as a\n * Unicode string.\n * @returns {String} The Punycode representation of the given domain name or\n * email address.\n */\nconst toASCII = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexNonASCII.test(string)\n\t\t\t? 'xn--' + encode(string)\n\t\t\t: string;\n\t});\n};\n\n/*--------------------------------------------------------------------------*/\n\n/** Define the public API */\nconst punycode = {\n\t/**\n\t * A string representing the current Punycode.js version number.\n\t * @memberOf punycode\n\t * @type String\n\t */\n\t'version': '2.3.1',\n\t/**\n\t * An object of methods to convert from JavaScript's internal character\n\t * representation (UCS-2) to Unicode code points, and back.\n\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t * @memberOf punycode\n\t * @type Object\n\t */\n\t'ucs2': {\n\t\t'decode': ucs2decode,\n\t\t'encode': ucs2encode\n\t},\n\t'decode': decode,\n\t'encode': encode,\n\t'toASCII': toASCII,\n\t'toUnicode': toUnicode\n};\n\nexport { ucs2decode, ucs2encode, decode, encode, toASCII, toUnicode };\nexport default punycode;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA,+CAA+C,GAC/C,MAAM,SAAS,YAAY,4BAA4B;AAEvD,0BAA0B,GAC1B,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,cAAc;AACpB,MAAM,WAAW,KAAK,OAAO;AAC7B,MAAM,YAAY,KAAK,SAAS;AAEhC,wBAAwB,GACxB,MAAM,gBAAgB;AACtB,MAAM,gBAAgB,cAAc,oCAAoC;AACxE,MAAM,kBAAkB,6BAA6B,sBAAsB;AAE3E,mBAAmB,GACnB,MAAM,SAAS;IACd,YAAY;IACZ,aAAa;IACb,iBAAiB;AAClB;AAEA,0BAA0B,GAC1B,MAAM,gBAAgB,OAAO;AAC7B,MAAM,QAAQ,KAAK,KAAK;AACxB,MAAM,qBAAqB,OAAO,YAAY;AAE9C,4EAA4E,GAE5E;;;;;CAKC,GACD,SAAS,MAAM,IAAI;IAClB,MAAM,IAAI,WAAW,MAAM,CAAC,KAAK;AAClC;AAEA;;;;;;;CAOC,GACD,SAAS,IAAI,KAAK,EAAE,QAAQ;IAC3B,MAAM,SAAS,EAAE;IACjB,IAAI,SAAS,MAAM,MAAM;IACzB,MAAO,SAAU;QAChB,MAAM,CAAC,OAAO,GAAG,SAAS,KAAK,CAAC,OAAO;IACxC;IACA,OAAO;AACR;AAEA;;;;;;;;;CASC,GACD,SAAS,UAAU,MAAM,EAAE,QAAQ;IAClC,MAAM,QAAQ,OAAO,KAAK,CAAC;IAC3B,IAAI,SAAS;IACb,IAAI,MAAM,MAAM,GAAG,GAAG;QACrB,sEAAsE;QACtE,qDAAqD;QACrD,SAAS,KAAK,CAAC,EAAE,GAAG;QACpB,SAAS,KAAK,CAAC,EAAE;IAClB;IACA,uDAAuD;IACvD,SAAS,OAAO,OAAO,CAAC,iBAAiB;IACzC,MAAM,SAAS,OAAO,KAAK,CAAC;IAC5B,MAAM,UAAU,IAAI,QAAQ,UAAU,IAAI,CAAC;IAC3C,OAAO,SAAS;AACjB;AAEA;;;;;;;;;;;;CAYC,GACD,SAAS,WAAW,MAAM;IACzB,MAAM,SAAS,EAAE;IACjB,IAAI,UAAU;IACd,MAAM,SAAS,OAAO,MAAM;IAC5B,MAAO,UAAU,OAAQ;QACxB,MAAM,QAAQ,OAAO,UAAU,CAAC;QAChC,IAAI,SAAS,UAAU,SAAS,UAAU,UAAU,QAAQ;YAC3D,wDAAwD;YACxD,MAAM,QAAQ,OAAO,UAAU,CAAC;YAChC,IAAI,CAAC,QAAQ,MAAM,KAAK,QAAQ;gBAC/B,OAAO,IAAI,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,EAAE,IAAI,CAAC,QAAQ,KAAK,IAAI;YACzD,OAAO;gBACN,uEAAuE;gBACvE,4DAA4D;gBAC5D,OAAO,IAAI,CAAC;gBACZ;YACD;QACD,OAAO;YACN,OAAO,IAAI,CAAC;QACb;IACD;IACA,OAAO;AACR;AAEA;;;;;;;CAOC,GACD,MAAM,aAAa,CAAA,aAAc,OAAO,aAAa,IAAI;AAEzD;;;;;;;;CAQC,GACD,MAAM,eAAe,SAAS,SAAS;IACtC,IAAI,aAAa,QAAQ,YAAY,MAAM;QAC1C,OAAO,KAAK,CAAC,YAAY,IAAI;IAC9B;IACA,IAAI,aAAa,QAAQ,YAAY,MAAM;QAC1C,OAAO,YAAY;IACpB;IACA,IAAI,aAAa,QAAQ,YAAY,MAAM;QAC1C,OAAO,YAAY;IACpB;IACA,OAAO;AACR;AAEA;;;;;;;;;;CAUC,GACD,MAAM,eAAe,SAAS,KAAK,EAAE,IAAI;IACxC,mCAAmC;IACnC,2BAA2B;IAC3B,OAAO,QAAQ,KAAK,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC1D;AAEA;;;;CAIC,GACD,MAAM,QAAQ,SAAS,KAAK,EAAE,SAAS,EAAE,SAAS;IACjD,IAAI,IAAI;IACR,QAAQ,YAAY,MAAM,QAAQ,QAAQ,SAAS;IACnD,SAAS,MAAM,QAAQ;IACvB,MAA8B,QAAQ,gBAAgB,QAAQ,GAAG,KAAK,KAAM;QAC3E,QAAQ,MAAM,QAAQ;IACvB;IACA,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,QAAQ,CAAC,QAAQ,IAAI;AAC7D;AAEA;;;;;;CAMC,GACD,MAAM,SAAS,SAAS,KAAK;IAC5B,mBAAmB;IACnB,MAAM,SAAS,EAAE;IACjB,MAAM,cAAc,MAAM,MAAM;IAChC,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,OAAO;IAEX,wEAAwE;IACxE,uEAAuE;IACvE,6CAA6C;IAE7C,IAAI,QAAQ,MAAM,WAAW,CAAC;IAC9B,IAAI,QAAQ,GAAG;QACd,QAAQ;IACT;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,EAAE,EAAG;QAC/B,iCAAiC;QACjC,IAAI,MAAM,UAAU,CAAC,MAAM,MAAM;YAChC,MAAM;QACP;QACA,OAAO,IAAI,CAAC,MAAM,UAAU,CAAC;IAC9B;IAEA,4EAA4E;IAC5E,wDAAwD;IAExD,IAAK,IAAI,QAAQ,QAAQ,IAAI,QAAQ,IAAI,GAAG,QAAQ,aAAwC;QAE3F,6DAA6D;QAC7D,6DAA6D;QAC7D,2DAA2D;QAC3D,8DAA8D;QAC9D,sCAAsC;QACtC,MAAM,OAAO;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,OAA0B,KAAK,KAAM;YAExD,IAAI,SAAS,aAAa;gBACzB,MAAM;YACP;YAEA,MAAM,QAAQ,aAAa,MAAM,UAAU,CAAC;YAE5C,IAAI,SAAS,MAAM;gBAClB,MAAM;YACP;YACA,IAAI,QAAQ,MAAM,CAAC,SAAS,CAAC,IAAI,IAAI;gBACpC,MAAM;YACP;YAEA,KAAK,QAAQ;YACb,MAAM,IAAI,KAAK,OAAO,OAAQ,KAAK,OAAO,OAAO,OAAO,IAAI;YAE5D,IAAI,QAAQ,GAAG;gBACd;YACD;YAEA,MAAM,aAAa,OAAO;YAC1B,IAAI,IAAI,MAAM,SAAS,aAAa;gBACnC,MAAM;YACP;YAEA,KAAK;QAEN;QAEA,MAAM,MAAM,OAAO,MAAM,GAAG;QAC5B,OAAO,MAAM,IAAI,MAAM,KAAK,QAAQ;QAEpC,qDAAqD;QACrD,qDAAqD;QACrD,IAAI,MAAM,IAAI,OAAO,SAAS,GAAG;YAChC,MAAM;QACP;QAEA,KAAK,MAAM,IAAI;QACf,KAAK;QAEL,4CAA4C;QAC5C,OAAO,MAAM,CAAC,KAAK,GAAG;IAEvB;IAEA,OAAO,OAAO,aAAa,IAAI;AAChC;AAEA;;;;;;CAMC,GACD,MAAM,SAAS,SAAS,KAAK;IAC5B,MAAM,SAAS,EAAE;IAEjB,iEAAiE;IACjE,QAAQ,WAAW;IAEnB,oBAAoB;IACpB,MAAM,cAAc,MAAM,MAAM;IAEhC,wBAAwB;IACxB,IAAI,IAAI;IACR,IAAI,QAAQ;IACZ,IAAI,OAAO;IAEX,gCAAgC;IAChC,KAAK,MAAM,gBAAgB,MAAO;QACjC,IAAI,eAAe,MAAM;YACxB,OAAO,IAAI,CAAC,mBAAmB;QAChC;IACD;IAEA,MAAM,cAAc,OAAO,MAAM;IACjC,IAAI,iBAAiB;IAErB,wEAAwE;IACxE,oDAAoD;IAEpD,8DAA8D;IAC9D,IAAI,aAAa;QAChB,OAAO,IAAI,CAAC;IACb;IAEA,sBAAsB;IACtB,MAAO,iBAAiB,YAAa;QAEpC,yEAAyE;QACzE,cAAc;QACd,IAAI,IAAI;QACR,KAAK,MAAM,gBAAgB,MAAO;YACjC,IAAI,gBAAgB,KAAK,eAAe,GAAG;gBAC1C,IAAI;YACL;QACD;QAEA,yEAAyE;QACzE,8BAA8B;QAC9B,MAAM,wBAAwB,iBAAiB;QAC/C,IAAI,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,wBAAwB;YAC5D,MAAM;QACP;QAEA,SAAS,CAAC,IAAI,CAAC,IAAI;QACnB,IAAI;QAEJ,KAAK,MAAM,gBAAgB,MAAO;YACjC,IAAI,eAAe,KAAK,EAAE,QAAQ,QAAQ;gBACzC,MAAM;YACP;YACA,IAAI,iBAAiB,GAAG;gBACvB,4DAA4D;gBAC5D,IAAI,IAAI;gBACR,IAAK,IAAI,IAAI,OAA0B,KAAK,KAAM;oBACjD,MAAM,IAAI,KAAK,OAAO,OAAQ,KAAK,OAAO,OAAO,OAAO,IAAI;oBAC5D,IAAI,IAAI,GAAG;wBACV;oBACD;oBACA,MAAM,UAAU,IAAI;oBACpB,MAAM,aAAa,OAAO;oBAC1B,OAAO,IAAI,CACV,mBAAmB,aAAa,IAAI,UAAU,YAAY;oBAE3D,IAAI,MAAM,UAAU;gBACrB;gBAEA,OAAO,IAAI,CAAC,mBAAmB,aAAa,GAAG;gBAC/C,OAAO,MAAM,OAAO,uBAAuB,mBAAmB;gBAC9D,QAAQ;gBACR,EAAE;YACH;QACD;QAEA,EAAE;QACF,EAAE;IAEH;IACA,OAAO,OAAO,IAAI,CAAC;AACpB;AAEA;;;;;;;;;;CAUC,GACD,MAAM,YAAY,SAAS,KAAK;IAC/B,OAAO,UAAU,OAAO,SAAS,MAAM;QACtC,OAAO,cAAc,IAAI,CAAC,UACvB,OAAO,OAAO,KAAK,CAAC,GAAG,WAAW,MAClC;IACJ;AACD;AAEA;;;;;;;;;;CAUC,GACD,MAAM,UAAU,SAAS,KAAK;IAC7B,OAAO,UAAU,OAAO,SAAS,MAAM;QACtC,OAAO,cAAc,IAAI,CAAC,UACvB,SAAS,OAAO,UAChB;IACJ;AACD;AAEA,4EAA4E,GAE5E,0BAA0B,GAC1B,MAAM,WAAW;IAChB;;;;EAIC,GACD,WAAW;IACX;;;;;;EAMC,GACD,QAAQ;QACP,UAAU;QACV,UAAU;IACX;IACA,UAAU;IACV,UAAU;IACV,WAAW;IACX,aAAa;AACd;;uCAGe", "ignoreList": [0], "debugId": null}}]}