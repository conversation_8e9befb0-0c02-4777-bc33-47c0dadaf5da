{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/utils/text-api.ts"], "sourcesContent": ["import { TextState, TextRang<PERSON>, TextAreaAPI } from '../types'\n\n/**\n * Insert text at a specific position in a textarea\n */\nexport function insertTextAtPosition(textarea: HTMLTextAreaElement, text: string, position?: number): void {\n  const pos = position ?? textarea.selectionStart\n  const before = textarea.value.substring(0, pos)\n  const after = textarea.value.substring(pos)\n  \n  textarea.value = before + text + after\n  textarea.selectionStart = textarea.selectionEnd = pos + text.length\n  \n  // Trigger change event\n  const event = new Event('input', { bubbles: true })\n  textarea.dispatchEvent(event)\n}\n\n/**\n * Get current state from textarea element\n */\nexport function getStateFromTextArea(textarea: HTMLTextAreaElement): TextState {\n  return {\n    selection: {\n      start: textarea.selectionStart,\n      end: textarea.selectionEnd,\n    },\n    text: textarea.value,\n    selectedText: textarea.value.slice(textarea.selectionStart, textarea.selectionEnd),\n  }\n}\n\n/**\n * TextArea API implementation for command execution\n */\nexport class TextAreaAPIImpl implements TextAreaAPI {\n  private textarea: HTMLTextAreaElement\n\n  constructor(textarea: HTMLTextAreaElement) {\n    this.textarea = textarea\n  }\n\n  /**\n   * Replace the current selection with new text\n   */\n  replaceSelection(text: string): TextState {\n    const start = this.textarea.selectionStart\n    const end = this.textarea.selectionEnd\n    const before = this.textarea.value.substring(0, start)\n    const after = this.textarea.value.substring(end)\n    \n    this.textarea.value = before + text + after\n    this.textarea.selectionStart = this.textarea.selectionEnd = start + text.length\n    \n    // Focus and trigger change event\n    this.textarea.focus()\n    const event = new Event('input', { bubbles: true })\n    this.textarea.dispatchEvent(event)\n    \n    return this.getState()\n  }\n\n  /**\n   * Set selection range\n   */\n  setSelectionRange(selection: TextRange): TextState {\n    this.textarea.focus()\n    this.textarea.selectionStart = selection.start\n    this.textarea.selectionEnd = selection.end\n    return this.getState()\n  }\n\n  /**\n   * Insert text at specific position\n   */\n  insertText(text: string, position?: number): TextState {\n    insertTextAtPosition(this.textarea, text, position)\n    return this.getState()\n  }\n\n  /**\n   * Get current state\n   */\n  getState(): TextState {\n    return getStateFromTextArea(this.textarea)\n  }\n}\n\n/**\n * Command orchestrator for handling command execution\n */\nexport class CommandOrchestrator {\n  private textarea: HTMLTextAreaElement\n  private textApi: TextAreaAPIImpl\n\n  constructor(textarea: HTMLTextAreaElement) {\n    this.textarea = textarea\n    this.textApi = new TextAreaAPIImpl(textarea)\n  }\n\n  getTextApi(): TextAreaAPIImpl {\n    return this.textApi\n  }\n\n  getState(): TextState | false {\n    if (!this.textarea) return false\n    return getStateFromTextArea(this.textarea)\n  }\n}\n\n/**\n * Utility functions for text manipulation\n */\n\n/**\n * Wrap selected text with prefix and suffix\n */\nexport function wrapText(state: TextState, api: TextAreaAPI, prefix: string, suffix: string = prefix): void {\n  const { selectedText } = state\n  const newText = `${prefix}${selectedText}${suffix}`\n  api.replaceSelection(newText)\n}\n\n/**\n * Insert text at the beginning of each line\n */\nexport function insertAtLineStart(state: TextState, api: TextAreaAPI, prefix: string): void {\n  const { selectedText } = state\n  const lines = selectedText.split('\\n')\n  const newText = lines.map(line => `${prefix}${line}`).join('\\n')\n  api.replaceSelection(newText)\n}\n\n/**\n * Toggle line prefix (add if not present, remove if present)\n */\nexport function toggleLinePrefix(state: TextState, api: TextAreaAPI, prefix: string): void {\n  const { selectedText } = state\n  const lines = selectedText.split('\\n')\n  \n  // Check if all lines start with prefix\n  const allHavePrefix = lines.every(line => line.startsWith(prefix))\n  \n  let newText: string\n  if (allHavePrefix) {\n    // Remove prefix from all lines\n    newText = lines.map(line => line.startsWith(prefix) ? line.slice(prefix.length) : line).join('\\n')\n  } else {\n    // Add prefix to all lines\n    newText = lines.map(line => `${prefix}${line}`).join('\\n')\n  }\n  \n  api.replaceSelection(newText)\n}\n\n/**\n * Insert text with proper line breaks\n */\nexport function insertBlock(state: TextState, api: TextAreaAPI, text: string): void {\n  const { selection } = state\n  const beforeText = state.text.substring(0, selection.start)\n  const afterText = state.text.substring(selection.end)\n  \n  // Add line breaks if needed\n  let prefix = ''\n  let suffix = ''\n  \n  if (beforeText && !beforeText.endsWith('\\n')) {\n    prefix = '\\n'\n  }\n  \n  if (afterText && !afterText.startsWith('\\n')) {\n    suffix = '\\n'\n  }\n  \n  api.replaceSelection(`${prefix}${text}${suffix}`)\n}\n\n/**\n * Get word count statistics\n */\nexport function getWordCount(text: string) {\n  const words = text.trim().split(/\\s+/).filter(word => word.length > 0).length\n  const characters = text.length\n  const charactersNoSpaces = text.replace(/\\s/g, '').length\n  const lines = text.split('\\n').length\n  \n  return {\n    words,\n    characters,\n    charactersNoSpaces,\n    lines,\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAKO,SAAS,qBAAqB,QAA6B,EAAE,IAAY,EAAE,QAAiB;IACjG,MAAM,MAAM,YAAY,SAAS,cAAc;IAC/C,MAAM,SAAS,SAAS,KAAK,CAAC,SAAS,CAAC,GAAG;IAC3C,MAAM,QAAQ,SAAS,KAAK,CAAC,SAAS,CAAC;IAEvC,SAAS,KAAK,GAAG,SAAS,OAAO;IACjC,SAAS,cAAc,GAAG,SAAS,YAAY,GAAG,MAAM,KAAK,MAAM;IAEnE,uBAAuB;IACvB,MAAM,QAAQ,IAAI,MAAM,SAAS;QAAE,SAAS;IAAK;IACjD,SAAS,aAAa,CAAC;AACzB;AAKO,SAAS,qBAAqB,QAA6B;IAChE,OAAO;QACL,WAAW;YACT,OAAO,SAAS,cAAc;YAC9B,KAAK,SAAS,YAAY;QAC5B;QACA,MAAM,SAAS,KAAK;QACpB,cAAc,SAAS,KAAK,CAAC,KAAK,CAAC,SAAS,cAAc,EAAE,SAAS,YAAY;IACnF;AACF;AAKO,MAAM;IACH,SAA6B;IAErC,YAAY,QAA6B,CAAE;QACzC,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA;;GAEC,GACD,iBAAiB,IAAY,EAAa;QACxC,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,cAAc;QAC1C,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY;QACtC,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG;QAChD,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC;QAE5C,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,SAAS,OAAO;QACtC,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,QAAQ,KAAK,MAAM;QAE/E,iCAAiC;QACjC,IAAI,CAAC,QAAQ,CAAC,KAAK;QACnB,MAAM,QAAQ,IAAI,MAAM,SAAS;YAAE,SAAS;QAAK;QACjD,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;QAE5B,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA;;GAEC,GACD,kBAAkB,SAAoB,EAAa;QACjD,IAAI,CAAC,QAAQ,CAAC,KAAK;QACnB,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,UAAU,KAAK;QAC9C,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,UAAU,GAAG;QAC1C,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA;;GAEC,GACD,WAAW,IAAY,EAAE,QAAiB,EAAa;QACrD,qBAAqB,IAAI,CAAC,QAAQ,EAAE,MAAM;QAC1C,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA;;GAEC,GACD,WAAsB;QACpB,OAAO,qBAAqB,IAAI,CAAC,QAAQ;IAC3C;AACF;AAKO,MAAM;IACH,SAA6B;IAC7B,QAAwB;IAEhC,YAAY,QAA6B,CAAE;QACzC,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,OAAO,GAAG,IAAI,gBAAgB;IACrC;IAEA,aAA8B;QAC5B,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA,WAA8B;QAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAC3B,OAAO,qBAAqB,IAAI,CAAC,QAAQ;IAC3C;AACF;AASO,SAAS,SAAS,KAAgB,EAAE,GAAgB,EAAE,MAAc,EAAE,SAAiB,MAAM;IAClG,MAAM,EAAE,YAAY,EAAE,GAAG;IACzB,MAAM,UAAU,GAAG,SAAS,eAAe,QAAQ;IACnD,IAAI,gBAAgB,CAAC;AACvB;AAKO,SAAS,kBAAkB,KAAgB,EAAE,GAAgB,EAAE,MAAc;IAClF,MAAM,EAAE,YAAY,EAAE,GAAG;IACzB,MAAM,QAAQ,aAAa,KAAK,CAAC;IACjC,MAAM,UAAU,MAAM,GAAG,CAAC,CAAA,OAAQ,GAAG,SAAS,MAAM,EAAE,IAAI,CAAC;IAC3D,IAAI,gBAAgB,CAAC;AACvB;AAKO,SAAS,iBAAiB,KAAgB,EAAE,GAAgB,EAAE,MAAc;IACjF,MAAM,EAAE,YAAY,EAAE,GAAG;IACzB,MAAM,QAAQ,aAAa,KAAK,CAAC;IAEjC,uCAAuC;IACvC,MAAM,gBAAgB,MAAM,KAAK,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC;IAE1D,IAAI;IACJ,IAAI,eAAe;QACjB,+BAA+B;QAC/B,UAAU,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC,UAAU,KAAK,KAAK,CAAC,OAAO,MAAM,IAAI,MAAM,IAAI,CAAC;IAC/F,OAAO;QACL,0BAA0B;QAC1B,UAAU,MAAM,GAAG,CAAC,CAAA,OAAQ,GAAG,SAAS,MAAM,EAAE,IAAI,CAAC;IACvD;IAEA,IAAI,gBAAgB,CAAC;AACvB;AAKO,SAAS,YAAY,KAAgB,EAAE,GAAgB,EAAE,IAAY;IAC1E,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,MAAM,aAAa,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,UAAU,KAAK;IAC1D,MAAM,YAAY,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG;IAEpD,4BAA4B;IAC5B,IAAI,SAAS;IACb,IAAI,SAAS;IAEb,IAAI,cAAc,CAAC,WAAW,QAAQ,CAAC,OAAO;QAC5C,SAAS;IACX;IAEA,IAAI,aAAa,CAAC,UAAU,UAAU,CAAC,OAAO;QAC5C,SAAS;IACX;IAEA,IAAI,gBAAgB,CAAC,GAAG,SAAS,OAAO,QAAQ;AAClD;AAKO,SAAS,aAAa,IAAY;IACvC,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IAC7E,MAAM,aAAa,KAAK,MAAM;IAC9B,MAAM,qBAAqB,KAAK,OAAO,CAAC,OAAO,IAAI,MAAM;IACzD,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,MAAM;IAErC,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/commands/index.tsx"], "sourcesContent": ["import { Command } from '../types'\nimport { wrapText, insertBlock, toggleLinePrefix } from '../utils/text-api'\n\n// Heading commands\nexport const h1: Command = {\n  name: 'h1',\n  keyCommand: 'h1',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M4 12h8m-8-6v12m8-12v12M17 7v10M21 7v10\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Heading 1', title: 'Heading 1' },\n  execute: (state, api) => {\n    const text = state.selectedText || 'Heading 1'\n    api.replaceSelection(`# ${text}`)\n  },\n}\n\nexport const h2: Command = {\n  name: 'h2',\n  keyCommand: 'h2',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M4 12h8m-8-6v12m8-12v12M17 7h4l-4 5h4v3h-4\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Heading 2', title: 'Heading 2' },\n  execute: (state, api) => {\n    const text = state.selectedText || 'Heading 2'\n    api.replaceSelection(`## ${text}`)\n  },\n}\n\nexport const h3: Command = {\n  name: 'h3',\n  keyCommand: 'h3',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M4 12h8m-8-6v12m8-12v12M17 7h4l-2 3 2 3h-4\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Heading 3', title: 'Heading 3' },\n  execute: (state, api) => {\n    const text = state.selectedText || 'Heading 3'\n    api.replaceSelection(`### ${text}`)\n  },\n}\n\n// Heading group command\nexport const headingGroup: Command = {\n  name: 'heading',\n  keyCommand: 'heading',\n  groupName: 'heading',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M6 12h12M6 20V4M18 20V4\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Headings', title: 'Headings' },\n  children: [h1, h2, h3],\n}\n\n// Basic formatting commands\nexport const bold: Command = {\n  name: 'bold',\n  keyCommand: 'bold',\n  shortcuts: ['ctrl+b', 'cmd+b'],\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z\" />\n      <path d=\"M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Bold', title: 'Bold (Ctrl+B)' },\n  execute: (state, api) => {\n    wrapText(state, api, '**')\n  },\n}\n\nexport const italic: Command = {\n  name: 'italic',\n  keyCommand: 'italic',\n  shortcuts: ['ctrl+i', 'cmd+i'],\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M19 4h-9M14 20H5M15 4L9 20\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Italic', title: 'Italic (Ctrl+I)' },\n  execute: (state, api) => {\n    wrapText(state, api, '*')\n  },\n}\n\nexport const strikethrough: Command = {\n  name: 'strikethrough',\n  keyCommand: 'strikethrough',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M16 4H9a3 3 0 0 0-2.83 4M14 12a4 4 0 0 1 0 8H6\" />\n      <line x1=\"4\" y1=\"12\" x2=\"20\" y2=\"12\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Strikethrough', title: 'Strikethrough' },\n  execute: (state, api) => {\n    wrapText(state, api, '~~')\n  },\n}\n\nexport const code: Command = {\n  name: 'code',\n  keyCommand: 'code',\n  shortcuts: ['ctrl+`', 'cmd+`'],\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <polyline points=\"16,18 22,12 16,6\" />\n      <polyline points=\"8,6 2,12 8,18\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Inline code', title: 'Inline code (Ctrl+`)' },\n  execute: (state, api) => {\n    wrapText(state, api, '`')\n  },\n}\n\nexport const codeBlock: Command = {\n  name: 'codeBlock',\n  keyCommand: 'codeBlock',\n  shortcuts: ['ctrl+shift+c', 'cmd+shift+c'],\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\n      <polyline points=\"8,12 12,16 16,12\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Code block', title: 'Code block (Ctrl+Shift+C)' },\n  execute: (state, api) => {\n    const codeBlockText = state.selectedText || 'code'\n    insertBlock(state, api, `\\`\\`\\`\\n${codeBlockText}\\n\\`\\`\\``)\n  },\n}\n\n// Link and image commands\nexport const link: Command = {\n  name: 'link',\n  keyCommand: 'link',\n  shortcuts: ['ctrl+k', 'cmd+k'],\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\" />\n      <path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Link', title: 'Link (Ctrl+K)' },\n  execute: (state, api) => {\n    const linkText = state.selectedText || 'link text'\n    api.replaceSelection(`[${linkText}](url)`)\n  },\n}\n\nexport const image: Command = {\n  name: 'image',\n  keyCommand: 'image',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\n      <circle cx=\"8.5\" cy=\"8.5\" r=\"1.5\" />\n      <polyline points=\"21,15 16,10 5,21\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Image', title: 'Image' },\n  execute: (state, api) => {\n    const altText = state.selectedText || 'alt text'\n    api.replaceSelection(`![${altText}](image-url)`)\n  },\n}\n\n// List commands\nexport const unorderedList: Command = {\n  name: 'unorderedList',\n  keyCommand: 'unorderedList',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <line x1=\"8\" y1=\"6\" x2=\"21\" y2=\"6\" />\n      <line x1=\"8\" y1=\"12\" x2=\"21\" y2=\"12\" />\n      <line x1=\"8\" y1=\"18\" x2=\"21\" y2=\"18\" />\n      <line x1=\"3\" y1=\"6\" x2=\"3.01\" y2=\"6\" />\n      <line x1=\"3\" y1=\"12\" x2=\"3.01\" y2=\"12\" />\n      <line x1=\"3\" y1=\"18\" x2=\"3.01\" y2=\"18\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Unordered list', title: 'Unordered list' },\n  execute: (state, api) => {\n    if (state.selectedText) {\n      toggleLinePrefix(state, api, '- ')\n    } else {\n      api.replaceSelection('- ')\n    }\n  },\n}\n\nexport const orderedList: Command = {\n  name: 'orderedList',\n  keyCommand: 'orderedList',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <line x1=\"10\" y1=\"6\" x2=\"21\" y2=\"6\" />\n      <line x1=\"10\" y1=\"12\" x2=\"21\" y2=\"12\" />\n      <line x1=\"10\" y1=\"18\" x2=\"21\" y2=\"18\" />\n      <path d=\"M4 6h1v4\" />\n      <path d=\"M4 10h2\" />\n      <path d=\"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Ordered list', title: 'Ordered list' },\n  execute: (state, api) => {\n    if (state.selectedText) {\n      const lines = state.selectedText.split('\\n')\n      const newText = lines.map((line, index) => `${index + 1}. ${line}`).join('\\n')\n      api.replaceSelection(newText)\n    } else {\n      api.replaceSelection('1. ')\n    }\n  },\n}\n\nexport const checkedList: Command = {\n  name: 'checkedList',\n  keyCommand: 'checkedList',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <polyline points=\"9,11 12,14 22,4\" />\n      <path d=\"M21,12v7a2,2 0,0 1,-2,2H5a2,2 0,0 1,-2,-2V5a2,2 0,0 1,2,-2h11\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Task list', title: 'Task list' },\n  execute: (state, api) => {\n    if (state.selectedText) {\n      toggleLinePrefix(state, api, '- [ ] ')\n    } else {\n      api.replaceSelection('- [ ] ')\n    }\n  },\n}\n\n// Quote and divider\nexport const quote: Command = {\n  name: 'quote',\n  keyCommand: 'quote',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z\" />\n      <path d=\"M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Quote', title: 'Quote' },\n  execute: (state, api) => {\n    if (state.selectedText) {\n      toggleLinePrefix(state, api, '> ')\n    } else {\n      api.replaceSelection('> ')\n    }\n  },\n}\n\nexport const hr: Command = {\n  name: 'hr',\n  keyCommand: 'hr',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <line x1=\"3\" y1=\"12\" x2=\"21\" y2=\"12\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Horizontal rule', title: 'Horizontal rule' },\n  execute: (state, api) => {\n    insertBlock(state, api, '---')\n  },\n}\n\n// Divider (visual separator in toolbar)\nexport const divider: Command = {\n  name: 'divider',\n  keyCommand: 'divider',\n  render: () => (\n    <div className=\"w-px h-6 bg-border mx-1\" role=\"separator\" aria-orientation=\"vertical\" />\n  ),\n}\n\n// Preview mode commands\nexport const editMode: Command = {\n  name: 'edit',\n  keyCommand: 'edit',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\" />\n      <path d=\"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Edit mode', title: 'Edit mode' },\n  execute: (_state, _api, dispatch) => {\n    dispatch?.({ preview: 'edit' })\n  },\n}\n\nexport const previewMode: Command = {\n  name: 'preview',\n  keyCommand: 'preview',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\" />\n      <circle cx=\"12\" cy=\"12\" r=\"3\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Preview mode', title: 'Preview mode' },\n  execute: (_state, _api, dispatch) => {\n    dispatch?.({ preview: 'preview' })\n  },\n}\n\nexport const liveMode: Command = {\n  name: 'live',\n  keyCommand: 'live',\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\n      <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" />\n      <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Live mode', title: 'Live mode' },\n  execute: (_state, _api, dispatch) => {\n    dispatch?.({ preview: 'live' })\n  },\n}\n\nexport const fullscreen: Command = {\n  name: 'fullscreen',\n  keyCommand: 'fullscreen',\n  shortcuts: ['F11'],\n  icon: (\n    <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth={2}>\n      <path d=\"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\" />\n    </svg>\n  ),\n  buttonProps: { 'aria-label': 'Fullscreen', title: 'Fullscreen (F11)' },\n  execute: (_state, _api, dispatch, executeState) => {\n    dispatch?.({ fullscreen: !executeState?.fullscreen })\n  },\n}\n\n// Get default commands\nexport function getDefaultCommands(): Command[] {\n  return [\n    bold,\n    italic,\n    strikethrough,\n    divider,\n    headingGroup,\n    divider,\n    code,\n    codeBlock,\n    divider,\n    link,\n    image,\n    divider,\n    unorderedList,\n    orderedList,\n    checkedList,\n    divider,\n    quote,\n    hr,\n  ]\n}\n\n// Get default extra commands (right side of toolbar)\nexport function getDefaultExtraCommands(): Command[] {\n  return [\n    editMode,\n    liveMode,\n    previewMode,\n    divider,\n    fullscreen,\n  ]\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA;;;AAGO,MAAM,KAAc;IACzB,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;kBAC7F,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAa,OAAO;IAAY;IAC7D,SAAS,CAAC,OAAO;QACf,MAAM,OAAO,MAAM,YAAY,IAAI;QACnC,IAAI,gBAAgB,CAAC,CAAC,EAAE,EAAE,MAAM;IAClC;AACF;AAEO,MAAM,KAAc;IACzB,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;kBAC7F,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAa,OAAO;IAAY;IAC7D,SAAS,CAAC,OAAO;QACf,MAAM,OAAO,MAAM,YAAY,IAAI;QACnC,IAAI,gBAAgB,CAAC,CAAC,GAAG,EAAE,MAAM;IACnC;AACF;AAEO,MAAM,KAAc;IACzB,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;kBAC7F,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAa,OAAO;IAAY;IAC7D,SAAS,CAAC,OAAO;QACf,MAAM,OAAO,MAAM,YAAY,IAAI;QACnC,IAAI,gBAAgB,CAAC,CAAC,IAAI,EAAE,MAAM;IACpC;AACF;AAGO,MAAM,eAAwB;IACnC,MAAM;IACN,YAAY;IACZ,WAAW;IACX,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;kBAC7F,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAY,OAAO;IAAW;IAC3D,UAAU;QAAC;QAAI;QAAI;KAAG;AACxB;AAGO,MAAM,OAAgB;IAC3B,MAAM;IACN,YAAY;IACZ,WAAW;QAAC;QAAU;KAAQ;IAC9B,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAQ,OAAO;IAAgB;IAC5D,SAAS,CAAC,OAAO;QACf,CAAA,GAAA,+JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,KAAK;IACvB;AACF;AAEO,MAAM,SAAkB;IAC7B,MAAM;IACN,YAAY;IACZ,WAAW;QAAC;QAAU;KAAQ;IAC9B,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;kBAC7F,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAU,OAAO;IAAkB;IAChE,SAAS,CAAC,OAAO;QACf,CAAA,GAAA,+JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,KAAK;IACvB;AACF;AAEO,MAAM,gBAAyB;IACpC,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;;;;;;;;;;;;IAGpC,aAAa;QAAE,cAAc;QAAiB,OAAO;IAAgB;IACrE,SAAS,CAAC,OAAO;QACf,CAAA,GAAA,+JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,KAAK;IACvB;AACF;AAEO,MAAM,OAAgB;IAC3B,MAAM;IACN,YAAY;IACZ,WAAW;QAAC;QAAU;KAAQ;IAC9B,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAS,QAAO;;;;;;0BACjB,8OAAC;gBAAS,QAAO;;;;;;;;;;;;IAGrB,aAAa;QAAE,cAAc;QAAe,OAAO;IAAuB;IAC1E,SAAS,CAAC,OAAO;QACf,CAAA,GAAA,+JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,KAAK;IACvB;AACF;AAEO,MAAM,YAAqB;IAChC,MAAM;IACN,YAAY;IACZ,WAAW;QAAC;QAAgB;KAAc;IAC1C,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,IAAG;;;;;;0BACnD,8OAAC;gBAAS,QAAO;;;;;;;;;;;;IAGrB,aAAa;QAAE,cAAc;QAAc,OAAO;IAA4B;IAC9E,SAAS,CAAC,OAAO;QACf,MAAM,gBAAgB,MAAM,YAAY,IAAI;QAC5C,CAAA,GAAA,+JAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAK,CAAC,QAAQ,EAAE,cAAc,QAAQ,CAAC;IAC5D;AACF;AAGO,MAAM,OAAgB;IAC3B,MAAM;IACN,YAAY;IACZ,WAAW;QAAC;QAAU;KAAQ;IAC9B,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAQ,OAAO;IAAgB;IAC5D,SAAS,CAAC,OAAO;QACf,MAAM,WAAW,MAAM,YAAY,IAAI;QACvC,IAAI,gBAAgB,CAAC,CAAC,CAAC,EAAE,SAAS,MAAM,CAAC;IAC3C;AACF;AAEO,MAAM,QAAiB;IAC5B,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,IAAG;;;;;;0BACnD,8OAAC;gBAAO,IAAG;gBAAM,IAAG;gBAAM,GAAE;;;;;;0BAC5B,8OAAC;gBAAS,QAAO;;;;;;;;;;;;IAGrB,aAAa;QAAE,cAAc;QAAS,OAAO;IAAQ;IACrD,SAAS,CAAC,OAAO;QACf,MAAM,UAAU,MAAM,YAAY,IAAI;QACtC,IAAI,gBAAgB,CAAC,CAAC,EAAE,EAAE,QAAQ,YAAY,CAAC;IACjD;AACF;AAGO,MAAM,gBAAyB;IACpC,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,IAAG;;;;;;0BAC/B,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;;;;;;0BAChC,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;;;;;;0BAChC,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAO,IAAG;;;;;;0BACjC,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAO,IAAG;;;;;;0BAClC,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAO,IAAG;;;;;;;;;;;;IAGtC,aAAa;QAAE,cAAc;QAAkB,OAAO;IAAiB;IACvE,SAAS,CAAC,OAAO;QACf,IAAI,MAAM,YAAY,EAAE;YACtB,CAAA,GAAA,+JAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,KAAK;QAC/B,OAAO;YACL,IAAI,gBAAgB,CAAC;QACvB;IACF;AACF;AAEO,MAAM,cAAuB;IAClC,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;;;;;;0BAChC,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;;;;;;0BACjC,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;;;;;;0BACjC,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAgB,OAAO;IAAe;IACnE,SAAS,CAAC,OAAO;QACf,IAAI,MAAM,YAAY,EAAE;YACtB,MAAM,QAAQ,MAAM,YAAY,CAAC,KAAK,CAAC;YACvC,MAAM,UAAU,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,GAAG,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC;YACzE,IAAI,gBAAgB,CAAC;QACvB,OAAO;YACL,IAAI,gBAAgB,CAAC;QACvB;IACF;AACF;AAEO,MAAM,cAAuB;IAClC,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAS,QAAO;;;;;;0BACjB,8OAAC;gBAAK,GAAE;;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAa,OAAO;IAAY;IAC7D,SAAS,CAAC,OAAO;QACf,IAAI,MAAM,YAAY,EAAE;YACtB,CAAA,GAAA,+JAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,KAAK;QAC/B,OAAO;YACL,IAAI,gBAAgB,CAAC;QACvB;IACF;AACF;AAGO,MAAM,QAAiB;IAC5B,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAS,OAAO;IAAQ;IACrD,SAAS,CAAC,OAAO;QACf,IAAI,MAAM,YAAY,EAAE;YACtB,CAAA,GAAA,+JAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,KAAK;QAC/B,OAAO;YACL,IAAI,gBAAgB,CAAC;QACvB;IACF;AACF;AAEO,MAAM,KAAc;IACzB,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;kBAC7F,cAAA,8OAAC;YAAK,IAAG;YAAI,IAAG;YAAK,IAAG;YAAK,IAAG;;;;;;;;;;;IAGpC,aAAa;QAAE,cAAc;QAAmB,OAAO;IAAkB;IACzE,SAAS,CAAC,OAAO;QACf,CAAA,GAAA,+JAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAK;IAC1B;AACF;AAGO,MAAM,UAAmB;IAC9B,MAAM;IACN,YAAY;IACZ,QAAQ,kBACN,8OAAC;YAAI,WAAU;YAA0B,MAAK;YAAY,oBAAiB;;;;;;AAE/E;AAGO,MAAM,WAAoB;IAC/B,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAa,OAAO;IAAY;IAC7D,SAAS,CAAC,QAAQ,MAAM;QACtB,WAAW;YAAE,SAAS;QAAO;IAC/B;AACF;AAEO,MAAM,cAAuB;IAClC,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;;;;;;;IAG9B,aAAa;QAAE,cAAc;QAAgB,OAAO;IAAe;IACnE,SAAS,CAAC,QAAQ,MAAM;QACtB,WAAW;YAAE,SAAS;QAAU;IAClC;AACF;AAEO,MAAM,WAAoB;IAC/B,MAAM;IACN,YAAY;IACZ,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;;0BAC7F,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,IAAG;;;;;;0BACnD,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,IAAG;;;;;;0BAC/B,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;;;;;;;;;;;;IAGnC,aAAa;QAAE,cAAc;QAAa,OAAO;IAAY;IAC7D,SAAS,CAAC,QAAQ,MAAM;QACtB,WAAW;YAAE,SAAS;QAAO;IAC/B;AACF;AAEO,MAAM,aAAsB;IACjC,MAAM;IACN,YAAY;IACZ,WAAW;QAAC;KAAM;IAClB,oBACE,8OAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAa;kBAC7F,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;IAGZ,aAAa;QAAE,cAAc;QAAc,OAAO;IAAmB;IACrE,SAAS,CAAC,QAAQ,MAAM,UAAU;QAChC,WAAW;YAAE,YAAY,CAAC,cAAc;QAAW;IACrD;AACF;AAGO,SAAS;IACd,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAGO,SAAS;IACd,OAAO;QACL;QACA;QACA;QACA;QACA;KACD;AACH", "debugId": null}}, {"offset": {"line": 1138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useReducer, ReactNode } from 'react'\nimport { EditorState, EditorAction } from './types'\nimport { getDefaultCommands, getDefaultExtraCommands } from './commands'\n\n// Default editor state\nconst defaultState: EditorState = {\n  markdown: '',\n  preview: 'live',\n  fullscreen: false,\n  height: 400,\n  highlightEnable: true,\n  tabSize: 2,\n  defaultTabEnable: false,\n  scrollTop: 0,\n  scrollTopPreview: 0,\n  commands: getDefaultCommands(),\n  extraCommands: getDefaultExtraCommands(),\n  barPopup: {},\n}\n\n// Reducer function\nfunction editorReducer(state: EditorState, action: EditorAction): EditorState {\n  return { ...state, ...action }\n}\n\n// Context type\ninterface EditorContextType {\n  state: EditorState\n  dispatch: (action: EditorAction) => void\n}\n\n// Create context\nconst EditorContext = createContext<EditorContextType | undefined>(undefined)\n\n// Provider props\ninterface EditorProviderProps {\n  children: ReactNode\n  initialState?: Partial<EditorState>\n}\n\n// Provider component\nexport function EditorProvider({ children, initialState = {} }: EditorProviderProps) {\n  const [state, dispatch] = useReducer(editorReducer, {\n    ...defaultState,\n    ...initialState,\n  })\n\n  return (\n    <EditorContext.Provider value={{ state, dispatch }}>\n      {children}\n    </EditorContext.Provider>\n  )\n}\n\n// Hook to use editor context\nexport function useEditor() {\n  const context = useContext(EditorContext)\n  if (context === undefined) {\n    throw new Error('useEditor must be used within an EditorProvider')\n  }\n  return context\n}\n\n// Hook for editor state only\nexport function useEditorState() {\n  const { state } = useEditor()\n  return state\n}\n\n// Hook for editor dispatch only\nexport function useEditorDispatch() {\n  const { dispatch } = useEditor()\n  return dispatch\n}\n\n// Utility function to set group popup states\nexport function setGroupPopupStates(popupStates: Record<string, boolean>, value: boolean = false): Record<string, boolean> {\n  const newStates: Record<string, boolean> = {}\n  Object.keys(popupStates).forEach((key) => {\n    newStates[key] = value\n  })\n  return newStates\n}\n\n// Utility function to toggle a specific popup\nexport function togglePopup(popupStates: Record<string, boolean>, key: string): Record<string, boolean> {\n  return {\n    ...setGroupPopupStates(popupStates, false),\n    [key]: !popupStates[key],\n  }\n}\n\n// Utility function to close all popups\nexport function closeAllPopups(popupStates: Record<string, boolean>): Record<string, boolean> {\n  return setGroupPopupStates(popupStates, false)\n}\n\n// Export context for direct access if needed\nexport { EditorContext }\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AAJA;;;;AAMA,uBAAuB;AACvB,MAAM,eAA4B;IAChC,UAAU;IACV,SAAS;IACT,YAAY;IACZ,QAAQ;IACR,iBAAiB;IACjB,SAAS;IACT,kBAAkB;IAClB,WAAW;IACX,kBAAkB;IAClB,UAAU,CAAA,GAAA,6JAAA,CAAA,qBAAkB,AAAD;IAC3B,eAAe,CAAA,GAAA,6JAAA,CAAA,0BAAuB,AAAD;IACrC,UAAU,CAAC;AACb;AAEA,mBAAmB;AACnB,SAAS,cAAc,KAAkB,EAAE,MAAoB;IAC7D,OAAO;QAAE,GAAG,KAAK;QAAE,GAAG,MAAM;IAAC;AAC/B;AAQA,iBAAiB;AACjB,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAS5D,SAAS,eAAe,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,EAAuB;IACjF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,eAAe;QAClD,GAAG,YAAY;QACf,GAAG,YAAY;IACjB;IAEA,qBACE,8OAAC,cAAc,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAS;kBAC9C;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,OAAO;AACT;AAGO,SAAS,oBAAoB,WAAoC,EAAE,QAAiB,KAAK;IAC9F,MAAM,YAAqC,CAAC;IAC5C,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAC;QAChC,SAAS,CAAC,IAAI,GAAG;IACnB;IACA,OAAO;AACT;AAGO,SAAS,YAAY,WAAoC,EAAE,GAAW;IAC3E,OAAO;QACL,GAAG,oBAAoB,aAAa,MAAM;QAC1C,CAAC,IAAI,EAAE,CAAC,WAAW,CAAC,IAAI;IAC1B;AACF;AAGO,SAAS,eAAe,WAAoC;IACjE,OAAO,oBAAoB,aAAa;AAC1C", "debugId": null}}, {"offset": {"line": 1234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/components/textarea.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'\nimport { TextareaProps } from '../types'\nimport { useEditor } from '../context'\nimport { CommandOrchestrator } from '../utils/text-api'\n\n// Syntax highlighting for markdown\nfunction highlightMarkdown(text: string): string {\n  return text\n    // Headers\n    .replace(/^(#{1,6})\\s+(.*)$/gm, '<span class=\"text-blue-600 dark:text-blue-400 font-bold\">$1</span> <span class=\"text-gray-900 dark:text-gray-100 font-semibold\">$2</span>')\n    // Bold\n    .replace(/\\*\\*(.*?)\\*\\*/g, '<span class=\"font-bold text-gray-900 dark:text-gray-100\">**$1**</span>')\n    // Italic\n    .replace(/\\*(.*?)\\*/g, '<span class=\"italic text-gray-700 dark:text-gray-300\">*$1*</span>')\n    // Inline code\n    .replace(/`([^`]+)`/g, '<span class=\"bg-gray-100 dark:bg-gray-800 text-red-600 dark:text-red-400 px-1 rounded font-mono text-sm\">`$1`</span>')\n    // Links\n    .replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/g, '<span class=\"text-blue-600 dark:text-blue-400 underline\">[$1]($2)</span>')\n    // Code blocks\n    .replace(/^```[\\s\\S]*?```$/gm, '<span class=\"bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 block p-2 rounded font-mono text-sm\">$&</span>')\n    // Lists\n    .replace(/^(\\s*[-*+])\\s+(.*)$/gm, '<span class=\"text-purple-600 dark:text-purple-400\">$1</span> <span class=\"text-gray-900 dark:text-gray-100\">$2</span>')\n    .replace(/^(\\s*\\d+\\.)\\s+(.*)$/gm, '<span class=\"text-purple-600 dark:text-purple-400\">$1</span> <span class=\"text-gray-900 dark:text-gray-100\">$2</span>')\n    // Quotes\n    .replace(/^>\\s+(.*)$/gm, '<span class=\"text-gray-600 dark:text-gray-400 border-l-4 border-gray-300 dark:border-gray-600 pl-2 italic\">> $1</span>')\n    // Horizontal rules\n    .replace(/^---+$/gm, '<span class=\"text-gray-400 dark:text-gray-600\">$&</span>')\n}\n\nexport interface TextareaRef {\n  focus: () => void\n  blur: () => void\n  getTextArea: () => HTMLTextAreaElement | null\n}\n\nconst Textarea = forwardRef<TextareaRef, TextareaProps>(({\n  value = '',\n  onChange,\n  onScroll,\n  highlightEnable = true,\n  tabSize = 2,\n  defaultTabEnable = false,\n  renderTextarea,\n  className = '',\n  style,\n  ...props\n}, ref) => {\n  const { state, dispatch } = useEditor()\n  const textareaRef = useRef<HTMLTextAreaElement>(null)\n  const preRef = useRef<HTMLPreElement>(null)\n  const wrapRef = useRef<HTMLDivElement>(null)\n  const orchestratorRef = useRef<CommandOrchestrator | null>(null)\n\n  // Expose methods via ref\n  useImperativeHandle(ref, () => ({\n    focus: () => textareaRef.current?.focus(),\n    blur: () => textareaRef.current?.blur(),\n    getTextArea: () => textareaRef.current,\n  }))\n\n  // Initialize command orchestrator\n  useEffect(() => {\n    if (textareaRef.current) {\n      orchestratorRef.current = new CommandOrchestrator(textareaRef.current)\n      dispatch({\n        textarea: textareaRef.current,\n        textareaWrap: wrapRef.current || undefined,\n        textareaPre: preRef.current || undefined,\n      })\n    }\n  }, [dispatch])\n\n  // Handle tab key behavior\n  const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLTextAreaElement>) => {\n    if (event.key === 'Tab' && !defaultTabEnable) {\n      event.preventDefault()\n      const textarea = event.currentTarget\n      const start = textarea.selectionStart\n      const end = textarea.selectionEnd\n      const spaces = ' '.repeat(tabSize)\n      \n      // Insert spaces\n      const newValue = value.substring(0, start) + spaces + value.substring(end)\n      onChange?.({ ...event, currentTarget: { ...textarea, value: newValue } } as unknown as React.ChangeEvent<HTMLTextAreaElement>)\n      \n      // Set cursor position\n      setTimeout(() => {\n        textarea.selectionStart = textarea.selectionEnd = start + spaces.length\n      }, 0)\n    }\n    \n    // Handle other keyboard shortcuts\n    if (event.ctrlKey || event.metaKey) {\n      const key = event.key.toLowerCase()\n      const shortcuts = state.commands.concat(state.extraCommands)\n        .filter(cmd => cmd.shortcuts?.some(shortcut => \n          shortcut.toLowerCase().includes(key) && \n          ((shortcut.includes('ctrl') && event.ctrlKey) || (shortcut.includes('cmd') && event.metaKey))\n        ))\n      \n      if (shortcuts.length > 0) {\n        event.preventDefault()\n        const command = shortcuts[0]\n        if (command.execute && orchestratorRef.current) {\n          const textApi = orchestratorRef.current.getTextApi()\n          const textState = orchestratorRef.current.getState()\n          if (textState) {\n            command.execute(textState, textApi, dispatch)\n          }\n        }\n      }\n    }\n  }, [defaultTabEnable, tabSize, value, onChange, state.commands, state.extraCommands, dispatch])\n\n  // Sync scroll between textarea and pre\n  const handleTextareaScroll = useCallback((event: React.UIEvent<HTMLTextAreaElement>) => {\n    if (preRef.current && highlightEnable) {\n      preRef.current.scrollTop = event.currentTarget.scrollTop\n      preRef.current.scrollLeft = event.currentTarget.scrollLeft\n    }\n    onScroll?.(event)\n  }, [highlightEnable, onScroll])\n\n  // Update syntax highlighting\n  const highlightedContent = highlightEnable ? highlightMarkdown(value) : ''\n\n  // Custom textarea renderer\n  if (renderTextarea) {\n    return renderTextarea({\n      value,\n      onChange,\n      onScroll: handleTextareaScroll,\n      onKeyDown: handleKeyDown,\n      highlightEnable,\n      tabSize,\n      defaultTabEnable,\n      className,\n      style,\n      ...props,\n    })\n  }\n\n  // Safe window access for SSR compatibility\n  const isMobile = typeof window !== 'undefined' ? window.innerWidth < 768 : false\n  const fontSize = isMobile ? '16px' : '14px'\n  const padding = isMobile ? '12px' : '16px'\n\n  // Check if we're in fullscreen mode - if so, ensure text is always visible\n  const isFullscreen = state.fullscreen\n  const shouldShowText = !highlightEnable || isFullscreen\n\n  const textareaStyles: React.CSSProperties = {\n    resize: 'none',\n    outline: 'none',\n    border: 'none',\n    background: 'transparent',\n    // Fix for fullscreen text visibility: ensure text is always visible in fullscreen\n    color: shouldShowText ? 'hsl(var(--foreground))' : 'transparent',\n    caretColor: 'hsl(var(--foreground))',\n    fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace',\n    fontSize,\n    lineHeight: '1.6',\n    padding,\n    width: '100%',\n    height: '100%',\n    position: highlightEnable && !isFullscreen ? 'absolute' : 'relative',\n    top: 0,\n    left: 0,\n    zIndex: highlightEnable && !isFullscreen ? 2 : 1,\n    // Ensure text is visible in fullscreen mode\n    WebkitTextFillColor: shouldShowText ? 'hsl(var(--foreground))' : 'transparent',\n    ...style,\n  }\n\n  const preStyles: React.CSSProperties = {\n    margin: 0,\n    padding, // Match textarea padding\n    fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace',\n    fontSize, // Match textarea font size\n    lineHeight: '1.6',\n    whiteSpace: 'pre-wrap',\n    wordWrap: 'break-word',\n    overflow: 'hidden',\n    position: highlightEnable && !isFullscreen ? 'absolute' : 'relative',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    zIndex: 1,\n    pointerEvents: 'none',\n    background: 'transparent',\n    color: 'hsl(var(--foreground))',\n    // Hide syntax highlighting in fullscreen to prevent conflicts\n    display: highlightEnable && !isFullscreen ? 'block' : 'none',\n  }\n\n  return (\n    <div\n      ref={wrapRef}\n      className={`relative w-full h-full min-h-[300px] ${className}`}\n      style={{\n        position: 'relative',\n        // Ensure proper stacking in fullscreen mode\n        zIndex: isFullscreen ? 'auto' : 'initial'\n      }}\n    >\n      {highlightEnable && !isFullscreen && (\n        <pre\n          ref={preRef}\n          style={preStyles}\n          dangerouslySetInnerHTML={{ __html: highlightedContent }}\n          aria-hidden=\"true\"\n        />\n      )}\n      <textarea\n        ref={textareaRef}\n        value={value}\n        onChange={onChange}\n        onScroll={handleTextareaScroll}\n        onKeyDown={handleKeyDown}\n        style={textareaStyles}\n        spellCheck={false}\n        autoComplete=\"off\"\n        autoCorrect=\"off\"\n        autoCapitalize=\"off\"\n        data-gramm=\"false\"\n        {...props}\n      />\n    </div>\n  )\n})\n\nTextarea.displayName = 'Textarea'\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAOA,mCAAmC;AACnC,SAAS,kBAAkB,IAAY;IACrC,OAAO,IACL,UAAU;KACT,OAAO,CAAC,uBAAuB,4IAChC,OAAO;KACN,OAAO,CAAC,kBAAkB,yEAC3B,SAAS;KACR,OAAO,CAAC,cAAc,oEACvB,cAAc;KACb,OAAO,CAAC,cAAc,uHACvB,QAAQ;KACP,OAAO,CAAC,4BAA4B,2EACrC,cAAc;KACb,OAAO,CAAC,sBAAsB,4HAC/B,QAAQ;KACP,OAAO,CAAC,yBAAyB,yHACjC,OAAO,CAAC,yBAAyB,wHAClC,SAAS;KACR,OAAO,CAAC,gBAAgB,yHACzB,mBAAmB;KAClB,OAAO,CAAC,YAAY;AACzB;AAQA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAA8B,CAAC,EACvD,QAAQ,EAAE,EACV,QAAQ,EACR,QAAQ,EACR,kBAAkB,IAAI,EACtB,UAAU,CAAC,EACX,mBAAmB,KAAK,EACxB,cAAc,EACd,YAAY,EAAE,EACd,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD;IACpC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACtC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA8B;IAE3D,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAM,CAAC;YAC9B,OAAO,IAAM,YAAY,OAAO,EAAE;YAClC,MAAM,IAAM,YAAY,OAAO,EAAE;YACjC,aAAa,IAAM,YAAY,OAAO;QACxC,CAAC;IAED,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,EAAE;YACvB,gBAAgB,OAAO,GAAG,IAAI,+JAAA,CAAA,sBAAmB,CAAC,YAAY,OAAO;YACrE,SAAS;gBACP,UAAU,YAAY,OAAO;gBAC7B,cAAc,QAAQ,OAAO,IAAI;gBACjC,aAAa,OAAO,OAAO,IAAI;YACjC;QACF;IACF,GAAG;QAAC;KAAS;IAEb,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI,MAAM,GAAG,KAAK,SAAS,CAAC,kBAAkB;YAC5C,MAAM,cAAc;YACpB,MAAM,WAAW,MAAM,aAAa;YACpC,MAAM,QAAQ,SAAS,cAAc;YACrC,MAAM,MAAM,SAAS,YAAY;YACjC,MAAM,SAAS,IAAI,MAAM,CAAC;YAE1B,gBAAgB;YAChB,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG,SAAS,SAAS,MAAM,SAAS,CAAC;YACtE,WAAW;gBAAE,GAAG,KAAK;gBAAE,eAAe;oBAAE,GAAG,QAAQ;oBAAE,OAAO;gBAAS;YAAE;YAEvE,sBAAsB;YACtB,WAAW;gBACT,SAAS,cAAc,GAAG,SAAS,YAAY,GAAG,QAAQ,OAAO,MAAM;YACzE,GAAG;QACL;QAEA,kCAAkC;QAClC,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,EAAE;YAClC,MAAM,MAAM,MAAM,GAAG,CAAC,WAAW;YACjC,MAAM,YAAY,MAAM,QAAQ,CAAC,MAAM,CAAC,MAAM,aAAa,EACxD,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,EAAE,KAAK,CAAA,WACjC,SAAS,WAAW,GAAG,QAAQ,CAAC,QAChC,CAAC,AAAC,SAAS,QAAQ,CAAC,WAAW,MAAM,OAAO,IAAM,SAAS,QAAQ,CAAC,UAAU,MAAM,OAAO,AAAC;YAGhG,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,MAAM,cAAc;gBACpB,MAAM,UAAU,SAAS,CAAC,EAAE;gBAC5B,IAAI,QAAQ,OAAO,IAAI,gBAAgB,OAAO,EAAE;oBAC9C,MAAM,UAAU,gBAAgB,OAAO,CAAC,UAAU;oBAClD,MAAM,YAAY,gBAAgB,OAAO,CAAC,QAAQ;oBAClD,IAAI,WAAW;wBACb,QAAQ,OAAO,CAAC,WAAW,SAAS;oBACtC;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAkB;QAAS;QAAO;QAAU,MAAM,QAAQ;QAAE,MAAM,aAAa;QAAE;KAAS;IAE9F,uCAAuC;IACvC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,IAAI,OAAO,OAAO,IAAI,iBAAiB;YACrC,OAAO,OAAO,CAAC,SAAS,GAAG,MAAM,aAAa,CAAC,SAAS;YACxD,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,aAAa,CAAC,UAAU;QAC5D;QACA,WAAW;IACb,GAAG;QAAC;QAAiB;KAAS;IAE9B,6BAA6B;IAC7B,MAAM,qBAAqB,kBAAkB,kBAAkB,SAAS;IAExE,2BAA2B;IAC3B,IAAI,gBAAgB;QAClB,OAAO,eAAe;YACpB;YACA;YACA,UAAU;YACV,WAAW;YACX;YACA;YACA;YACA;YACA;YACA,GAAG,KAAK;QACV;IACF;IAEA,2CAA2C;IAC3C,MAAM,WAAW,6EAA0D;IAC3E,MAAM,WAAW,6EAAoB;IACrC,MAAM,UAAU,6EAAoB;IAEpC,2EAA2E;IAC3E,MAAM,eAAe,MAAM,UAAU;IACrC,MAAM,iBAAiB,CAAC,mBAAmB;IAE3C,MAAM,iBAAsC;QAC1C,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,YAAY;QACZ,kFAAkF;QAClF,OAAO,iBAAiB,2BAA2B;QACnD,YAAY;QACZ,YAAY;QACZ;QACA,YAAY;QACZ;QACA,OAAO;QACP,QAAQ;QACR,UAAU,mBAAmB,CAAC,eAAe,aAAa;QAC1D,KAAK;QACL,MAAM;QACN,QAAQ,mBAAmB,CAAC,eAAe,IAAI;QAC/C,4CAA4C;QAC5C,qBAAqB,iBAAiB,2BAA2B;QACjE,GAAG,KAAK;IACV;IAEA,MAAM,YAAiC;QACrC,QAAQ;QACR;QACA,YAAY;QACZ;QACA,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,UAAU;QACV,UAAU,mBAAmB,CAAC,eAAe,aAAa;QAC1D,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,eAAe;QACf,YAAY;QACZ,OAAO;QACP,8DAA8D;QAC9D,SAAS,mBAAmB,CAAC,eAAe,UAAU;IACxD;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,qCAAqC,EAAE,WAAW;QAC9D,OAAO;YACL,UAAU;YACV,4CAA4C;YAC5C,QAAQ,eAAe,SAAS;QAClC;;YAEC,mBAAmB,CAAC,8BACnB,8OAAC;gBACC,KAAK;gBACL,OAAO;gBACP,yBAAyB;oBAAE,QAAQ;gBAAmB;gBACtD,eAAY;;;;;;0BAGhB,8OAAC;gBACC,KAAK;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,YAAY;gBACZ,cAAa;gBACb,aAAY;gBACZ,gBAAe;gBACf,cAAW;gBACV,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/components/toolbar.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useRef, useEffect } from 'react'\nimport { Command, ToolbarProps } from '../types'\nimport { useEditor, togglePopup, closeAllPopups } from '../context'\n\ninterface CommandButtonProps {\n  command: Command\n  disabled?: boolean\n  executeCommand: (command: Command, groupName?: string) => void\n  index: number\n}\n\nfunction CommandButton({ command, disabled = false, executeCommand, index }: CommandButtonProps) {\n  const { state, dispatch } = useEditor()\n  const [isOpen, setIsOpen] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n\n  // Handle click outside to close dropdown\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside)\n      return () => document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [isOpen])\n\n  // Custom render function\n  if (command.render) {\n    return command.render(command, disabled, executeCommand, index) || null\n  }\n\n  // Divider\n  if (command.name === 'divider') {\n    return (\n      <div \n        className=\"w-px h-6 bg-border mx-1\" \n        role=\"separator\" \n        aria-orientation=\"vertical\" \n      />\n    )\n  }\n\n  // Group with children commands\n  if (command.children && Array.isArray(command.children)) {\n    return (\n      <div className=\"relative\" ref={dropdownRef}>\n        <button\n          type=\"button\"\n          disabled={disabled}\n          onClick={() => setIsOpen(!isOpen)}\n          className={`\n            inline-flex items-center justify-center\n            w-8 h-8 sm:w-8 sm:h-8 min-w-[44px] min-h-[44px] sm:min-w-[32px] sm:min-h-[32px]\n            rounded-md p-1 sm:p-0\n            hover:bg-accent hover:text-accent-foreground\n            focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\n            disabled:opacity-50 disabled:cursor-not-allowed\n            transition-colors duration-200\n            ${isOpen ? 'bg-accent text-accent-foreground' : 'text-muted-foreground'}\n          `}\n          aria-label={command.buttonProps?.['aria-label'] || command.name}\n          title={command.buttonProps?.title || command.name}\n          {...command.buttonProps}\n        >\n          {command.icon}\n          <svg \n            className=\"w-3 h-3 ml-1\" \n            fill=\"none\" \n            stroke=\"currentColor\" \n            viewBox=\"0 0 24 24\"\n          >\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n          </svg>\n        </button>\n\n        {isOpen && (\n          <div className=\"absolute top-full left-0 mt-1 bg-popover border border-border rounded-md shadow-lg z-50 min-w-[120px]\">\n            <div className=\"py-1\">\n              {command.children.map((childCommand, childIndex) => (\n                <button\n                  key={childCommand.name || childIndex}\n                  type=\"button\"\n                  disabled={disabled}\n                  onClick={() => {\n                    executeCommand(childCommand, command.groupName)\n                    setIsOpen(false)\n                  }}\n                  className=\"w-full px-3 py-2 text-left text-sm hover:bg-accent hover:text-accent-foreground flex items-center\"\n                  aria-label={childCommand.buttonProps?.['aria-label'] || childCommand.name}\n                >\n                  {childCommand.icon && (\n                    <span className=\"mr-2 flex-shrink-0\">\n                      {childCommand.icon}\n                    </span>\n                  )}\n                  {childCommand.name}\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  // Group with children function (popup content)\n  if (command.children && typeof command.children === 'function') {\n    const isPopupOpen = state.barPopup[command.groupName || command.name] || false\n\n    return (\n      <div className=\"relative\" ref={dropdownRef}>\n        <button\n          type=\"button\"\n          disabled={disabled}\n          onClick={() => {\n            dispatch({\n              barPopup: togglePopup(state.barPopup, command.groupName || command.name)\n            })\n          }}\n          className={`\n            inline-flex items-center justify-center\n            w-8 h-8 sm:w-8 sm:h-8 min-w-[44px] min-h-[44px] sm:min-w-[32px] sm:min-h-[32px]\n            rounded-md p-1 sm:p-0\n            hover:bg-accent hover:text-accent-foreground\n            focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\n            disabled:opacity-50 disabled:cursor-not-allowed\n            transition-colors duration-200\n            ${isPopupOpen ? 'bg-accent text-accent-foreground' : 'text-muted-foreground'}\n          `}\n          aria-label={command.buttonProps?.['aria-label'] || command.name}\n          title={command.buttonProps?.title || command.name}\n          {...command.buttonProps}\n        >\n          {command.icon}\n        </button>\n\n        {isPopupOpen && (\n          <div className=\"absolute top-full left-0 mt-1 bg-popover border border-border rounded-md shadow-lg z-50\">\n            {command.children({\n              close: () => dispatch({ \n                barPopup: closeAllPopups(state.barPopup) \n              }),\n              execute: () => executeCommand(command, command.groupName),\n              getState: () => state.textarea ? {\n                text: state.textarea.value,\n                selectedText: state.textarea.value.slice(\n                  state.textarea.selectionStart,\n                  state.textarea.selectionEnd\n                ),\n                selection: {\n                  start: state.textarea.selectionStart,\n                  end: state.textarea.selectionEnd,\n                }\n              } : false,\n              textApi: state.textarea ? {\n                replaceSelection: (text: string) => {\n                  if (state.textarea) {\n                    const start = state.textarea.selectionStart\n                    const end = state.textarea.selectionEnd\n                    const before = state.textarea.value.substring(0, start)\n                    const after = state.textarea.value.substring(end)\n                    state.textarea.value = before + text + after\n                    state.textarea.selectionStart = state.textarea.selectionEnd = start + text.length\n                    state.textarea.focus()\n                    const event = new Event('input', { bubbles: true })\n                    state.textarea.dispatchEvent(event)\n                  }\n                  return state.textarea ? {\n                    text: state.textarea.value,\n                    selectedText: '',\n                    selection: {\n                      start: state.textarea.selectionStart,\n                      end: state.textarea.selectionEnd,\n                    }\n                  } : { text: '', selectedText: '', selection: { start: 0, end: 0 } }\n                },\n                setSelectionRange: (selection) => {\n                  if (state.textarea) {\n                    state.textarea.focus()\n                    state.textarea.selectionStart = selection.start\n                    state.textarea.selectionEnd = selection.end\n                  }\n                  return state.textarea ? {\n                    text: state.textarea.value,\n                    selectedText: state.textarea.value.slice(selection.start, selection.end),\n                    selection,\n                  } : { text: '', selectedText: '', selection: { start: 0, end: 0 } }\n                },\n                insertText: (text: string, position?: number) => {\n                  if (state.textarea) {\n                    const pos = position ?? state.textarea.selectionStart\n                    const before = state.textarea.value.substring(0, pos)\n                    const after = state.textarea.value.substring(pos)\n                    state.textarea.value = before + text + after\n                    state.textarea.selectionStart = state.textarea.selectionEnd = pos + text.length\n                    state.textarea.focus()\n                    const event = new Event('input', { bubbles: true })\n                    state.textarea.dispatchEvent(event)\n                  }\n                  return state.textarea ? {\n                    text: state.textarea.value,\n                    selectedText: '',\n                    selection: {\n                      start: state.textarea.selectionStart,\n                      end: state.textarea.selectionEnd,\n                    }\n                  } : { text: '', selectedText: '', selection: { start: 0, end: 0 } }\n                },\n                getState: () => state.textarea ? {\n                  text: state.textarea.value,\n                  selectedText: state.textarea.value.slice(\n                    state.textarea.selectionStart,\n                    state.textarea.selectionEnd\n                  ),\n                  selection: {\n                    start: state.textarea.selectionStart,\n                    end: state.textarea.selectionEnd,\n                  }\n                } : { text: '', selectedText: '', selection: { start: 0, end: 0 } }\n              } : {\n                replaceSelection: () => ({ text: '', selectedText: '', selection: { start: 0, end: 0 } }),\n                setSelectionRange: () => ({ text: '', selectedText: '', selection: { start: 0, end: 0 } }),\n                insertText: () => ({ text: '', selectedText: '', selection: { start: 0, end: 0 } }),\n                getState: () => ({ text: '', selectedText: '', selection: { start: 0, end: 0 } })\n              }\n            })}\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  // Regular button\n  return (\n    <button\n      type=\"button\"\n      disabled={disabled}\n      onClick={() => executeCommand(command, command.groupName)}\n      className={`\n        inline-flex items-center justify-center\n        w-8 h-8 sm:w-8 sm:h-8 min-w-[44px] min-h-[44px] sm:min-w-[32px] sm:min-h-[32px]\n        rounded-md p-1 sm:p-0\n        hover:bg-accent hover:text-accent-foreground\n        focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\n        disabled:opacity-50 disabled:cursor-not-allowed\n        transition-colors duration-200\n        text-muted-foreground\n      `}\n      aria-label={command.buttonProps?.['aria-label'] || command.name}\n      title={command.buttonProps?.title || command.name}\n      {...command.buttonProps}\n    >\n      {command.icon}\n    </button>\n  )\n}\n\nexport function Toolbar({\n  commands,\n  extraCommands,\n  executeCommand,\n  className = '',\n  toolbarBottom = false\n}: ToolbarProps) {\n  const { state, dispatch } = useEditor()\n\n  const handleContainerClick = () => {\n    dispatch({ barPopup: closeAllPopups(state.barPopup) })\n  }\n\n  return (\n    <div\n      className={`\n        flex items-center justify-between\n        px-2 py-2 sm:px-3 sm:py-2\n        bg-card border-border toolbar\n        ${toolbarBottom ? 'border-t' : 'border-b'}\n        ${className}\n      `}\n      onClick={handleContainerClick}\n    >\n      {/* Left side commands */}\n      <div className=\"flex items-center space-x-0.5 sm:space-x-1 overflow-x-auto toolbar-commands\">\n        {commands.map((command, index) => (\n          <CommandButton\n            key={command.name || index}\n            command={command}\n            executeCommand={executeCommand}\n            index={index}\n          />\n        ))}\n      </div>\n\n      {/* Right side commands */}\n      {extraCommands.length > 0 && (\n        <div className=\"flex items-center space-x-0.5 sm:space-x-1 ml-2 sm:ml-4 flex-shrink-0\">\n          {extraCommands.map((command, index) => (\n            <CommandButton\n              key={command.name || index}\n              command={command}\n              executeCommand={executeCommand}\n              index={index}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAaA,SAAS,cAAc,EAAE,OAAO,EAAE,WAAW,KAAK,EAAE,cAAc,EAAE,KAAK,EAAsB;IAC7F,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD;IACpC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,aAAa;YACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;QACzD;IACF,GAAG;QAAC;KAAO;IAEX,yBAAyB;IACzB,IAAI,QAAQ,MAAM,EAAE;QAClB,OAAO,QAAQ,MAAM,CAAC,SAAS,UAAU,gBAAgB,UAAU;IACrE;IAEA,UAAU;IACV,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,qBACE,8OAAC;YACC,WAAU;YACV,MAAK;YACL,oBAAiB;;;;;;IAGvB;IAEA,+BAA+B;IAC/B,IAAI,QAAQ,QAAQ,IAAI,MAAM,OAAO,CAAC,QAAQ,QAAQ,GAAG;QACvD,qBACE,8OAAC;YAAI,WAAU;YAAW,KAAK;;8BAC7B,8OAAC;oBACC,MAAK;oBACL,UAAU;oBACV,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAW,CAAC;;;;;;;;YAQV,EAAE,SAAS,qCAAqC,wBAAwB;UAC1E,CAAC;oBACD,cAAY,QAAQ,WAAW,EAAE,CAAC,aAAa,IAAI,QAAQ,IAAI;oBAC/D,OAAO,QAAQ,WAAW,EAAE,SAAS,QAAQ,IAAI;oBAChD,GAAG,QAAQ,WAAW;;wBAEtB,QAAQ,IAAI;sCACb,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;sCAER,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;gBAIxE,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,cAAc,2BACnC,8OAAC;gCAEC,MAAK;gCACL,UAAU;gCACV,SAAS;oCACP,eAAe,cAAc,QAAQ,SAAS;oCAC9C,UAAU;gCACZ;gCACA,WAAU;gCACV,cAAY,aAAa,WAAW,EAAE,CAAC,aAAa,IAAI,aAAa,IAAI;;oCAExE,aAAa,IAAI,kBAChB,8OAAC;wCAAK,WAAU;kDACb,aAAa,IAAI;;;;;;oCAGrB,aAAa,IAAI;;+BAfb,aAAa,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;IAuB1C;IAEA,+CAA+C;IAC/C,IAAI,QAAQ,QAAQ,IAAI,OAAO,QAAQ,QAAQ,KAAK,YAAY;QAC9D,MAAM,cAAc,MAAM,QAAQ,CAAC,QAAQ,SAAS,IAAI,QAAQ,IAAI,CAAC,IAAI;QAEzE,qBACE,8OAAC;YAAI,WAAU;YAAW,KAAK;;8BAC7B,8OAAC;oBACC,MAAK;oBACL,UAAU;oBACV,SAAS;wBACP,SAAS;4BACP,UAAU,CAAA,GAAA,mJAAA,CAAA,cAAW,AAAD,EAAE,MAAM,QAAQ,EAAE,QAAQ,SAAS,IAAI,QAAQ,IAAI;wBACzE;oBACF;oBACA,WAAW,CAAC;;;;;;;;YAQV,EAAE,cAAc,qCAAqC,wBAAwB;UAC/E,CAAC;oBACD,cAAY,QAAQ,WAAW,EAAE,CAAC,aAAa,IAAI,QAAQ,IAAI;oBAC/D,OAAO,QAAQ,WAAW,EAAE,SAAS,QAAQ,IAAI;oBAChD,GAAG,QAAQ,WAAW;8BAEtB,QAAQ,IAAI;;;;;;gBAGd,6BACC,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,QAAQ,CAAC;wBAChB,OAAO,IAAM,SAAS;gCACpB,UAAU,CAAA,GAAA,mJAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;4BACzC;wBACA,SAAS,IAAM,eAAe,SAAS,QAAQ,SAAS;wBACxD,UAAU,IAAM,MAAM,QAAQ,GAAG;gCAC/B,MAAM,MAAM,QAAQ,CAAC,KAAK;gCAC1B,cAAc,MAAM,QAAQ,CAAC,KAAK,CAAC,KAAK,CACtC,MAAM,QAAQ,CAAC,cAAc,EAC7B,MAAM,QAAQ,CAAC,YAAY;gCAE7B,WAAW;oCACT,OAAO,MAAM,QAAQ,CAAC,cAAc;oCACpC,KAAK,MAAM,QAAQ,CAAC,YAAY;gCAClC;4BACF,IAAI;wBACJ,SAAS,MAAM,QAAQ,GAAG;4BACxB,kBAAkB,CAAC;gCACjB,IAAI,MAAM,QAAQ,EAAE;oCAClB,MAAM,QAAQ,MAAM,QAAQ,CAAC,cAAc;oCAC3C,MAAM,MAAM,MAAM,QAAQ,CAAC,YAAY;oCACvC,MAAM,SAAS,MAAM,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG;oCACjD,MAAM,QAAQ,MAAM,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC;oCAC7C,MAAM,QAAQ,CAAC,KAAK,GAAG,SAAS,OAAO;oCACvC,MAAM,QAAQ,CAAC,cAAc,GAAG,MAAM,QAAQ,CAAC,YAAY,GAAG,QAAQ,KAAK,MAAM;oCACjF,MAAM,QAAQ,CAAC,KAAK;oCACpB,MAAM,QAAQ,IAAI,MAAM,SAAS;wCAAE,SAAS;oCAAK;oCACjD,MAAM,QAAQ,CAAC,aAAa,CAAC;gCAC/B;gCACA,OAAO,MAAM,QAAQ,GAAG;oCACtB,MAAM,MAAM,QAAQ,CAAC,KAAK;oCAC1B,cAAc;oCACd,WAAW;wCACT,OAAO,MAAM,QAAQ,CAAC,cAAc;wCACpC,KAAK,MAAM,QAAQ,CAAC,YAAY;oCAClC;gCACF,IAAI;oCAAE,MAAM;oCAAI,cAAc;oCAAI,WAAW;wCAAE,OAAO;wCAAG,KAAK;oCAAE;gCAAE;4BACpE;4BACA,mBAAmB,CAAC;gCAClB,IAAI,MAAM,QAAQ,EAAE;oCAClB,MAAM,QAAQ,CAAC,KAAK;oCACpB,MAAM,QAAQ,CAAC,cAAc,GAAG,UAAU,KAAK;oCAC/C,MAAM,QAAQ,CAAC,YAAY,GAAG,UAAU,GAAG;gCAC7C;gCACA,OAAO,MAAM,QAAQ,GAAG;oCACtB,MAAM,MAAM,QAAQ,CAAC,KAAK;oCAC1B,cAAc,MAAM,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,KAAK,EAAE,UAAU,GAAG;oCACvE;gCACF,IAAI;oCAAE,MAAM;oCAAI,cAAc;oCAAI,WAAW;wCAAE,OAAO;wCAAG,KAAK;oCAAE;gCAAE;4BACpE;4BACA,YAAY,CAAC,MAAc;gCACzB,IAAI,MAAM,QAAQ,EAAE;oCAClB,MAAM,MAAM,YAAY,MAAM,QAAQ,CAAC,cAAc;oCACrD,MAAM,SAAS,MAAM,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG;oCACjD,MAAM,QAAQ,MAAM,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC;oCAC7C,MAAM,QAAQ,CAAC,KAAK,GAAG,SAAS,OAAO;oCACvC,MAAM,QAAQ,CAAC,cAAc,GAAG,MAAM,QAAQ,CAAC,YAAY,GAAG,MAAM,KAAK,MAAM;oCAC/E,MAAM,QAAQ,CAAC,KAAK;oCACpB,MAAM,QAAQ,IAAI,MAAM,SAAS;wCAAE,SAAS;oCAAK;oCACjD,MAAM,QAAQ,CAAC,aAAa,CAAC;gCAC/B;gCACA,OAAO,MAAM,QAAQ,GAAG;oCACtB,MAAM,MAAM,QAAQ,CAAC,KAAK;oCAC1B,cAAc;oCACd,WAAW;wCACT,OAAO,MAAM,QAAQ,CAAC,cAAc;wCACpC,KAAK,MAAM,QAAQ,CAAC,YAAY;oCAClC;gCACF,IAAI;oCAAE,MAAM;oCAAI,cAAc;oCAAI,WAAW;wCAAE,OAAO;wCAAG,KAAK;oCAAE;gCAAE;4BACpE;4BACA,UAAU,IAAM,MAAM,QAAQ,GAAG;oCAC/B,MAAM,MAAM,QAAQ,CAAC,KAAK;oCAC1B,cAAc,MAAM,QAAQ,CAAC,KAAK,CAAC,KAAK,CACtC,MAAM,QAAQ,CAAC,cAAc,EAC7B,MAAM,QAAQ,CAAC,YAAY;oCAE7B,WAAW;wCACT,OAAO,MAAM,QAAQ,CAAC,cAAc;wCACpC,KAAK,MAAM,QAAQ,CAAC,YAAY;oCAClC;gCACF,IAAI;oCAAE,MAAM;oCAAI,cAAc;oCAAI,WAAW;wCAAE,OAAO;wCAAG,KAAK;oCAAE;gCAAE;wBACpE,IAAI;4BACF,kBAAkB,IAAM,CAAC;oCAAE,MAAM;oCAAI,cAAc;oCAAI,WAAW;wCAAE,OAAO;wCAAG,KAAK;oCAAE;gCAAE,CAAC;4BACxF,mBAAmB,IAAM,CAAC;oCAAE,MAAM;oCAAI,cAAc;oCAAI,WAAW;wCAAE,OAAO;wCAAG,KAAK;oCAAE;gCAAE,CAAC;4BACzF,YAAY,IAAM,CAAC;oCAAE,MAAM;oCAAI,cAAc;oCAAI,WAAW;wCAAE,OAAO;wCAAG,KAAK;oCAAE;gCAAE,CAAC;4BAClF,UAAU,IAAM,CAAC;oCAAE,MAAM;oCAAI,cAAc;oCAAI,WAAW;wCAAE,OAAO;wCAAG,KAAK;oCAAE;gCAAE,CAAC;wBAClF;oBACF;;;;;;;;;;;;IAKV;IAEA,iBAAiB;IACjB,qBACE,8OAAC;QACC,MAAK;QACL,UAAU;QACV,SAAS,IAAM,eAAe,SAAS,QAAQ,SAAS;QACxD,WAAW,CAAC;;;;;;;;;MASZ,CAAC;QACD,cAAY,QAAQ,WAAW,EAAE,CAAC,aAAa,IAAI,QAAQ,IAAI;QAC/D,OAAO,QAAQ,WAAW,EAAE,SAAS,QAAQ,IAAI;QAChD,GAAG,QAAQ,WAAW;kBAEtB,QAAQ,IAAI;;;;;;AAGnB;AAEO,SAAS,QAAQ,EACtB,QAAQ,EACR,aAAa,EACb,cAAc,EACd,YAAY,EAAE,EACd,gBAAgB,KAAK,EACR;IACb,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD;IAEpC,MAAM,uBAAuB;QAC3B,SAAS;YAAE,UAAU,CAAA,GAAA,mJAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;QAAE;IACtD;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC;;;;QAIV,EAAE,gBAAgB,aAAa,WAAW;QAC1C,EAAE,UAAU;MACd,CAAC;QACD,SAAS;;0BAGT,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;wBAEC,SAAS;wBACT,gBAAgB;wBAChB,OAAO;uBAHF,QAAQ,IAAI,IAAI;;;;;;;;;;YAS1B,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,SAAS,sBAC3B,8OAAC;wBAEC,SAAS;wBACT,gBAAgB;wBAChB,OAAO;uBAHF,QAAQ,IAAI,IAAI;;;;;;;;;;;;;;;;AAUnC", "debugId": null}}, {"offset": {"line": 1889, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/components/preview.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useMemo, forwardRef } from 'react'\nimport MarkdownIt from 'markdown-it'\nimport hljs from 'highlight.js'\nimport { PreviewProps } from '../types'\n\n// Configure markdown-it with plugins\nconst createMarkdownRenderer = () => {\n  const md: MarkdownIt = new MarkdownIt({\n    html: true,\n    linkify: true,\n    typographer: true,\n    highlight: function (str, lang) {\n      if (lang && hljs.getLanguage(lang)) {\n        try {\n          return `<pre class=\"hljs\"><code class=\"hljs language-${lang}\">${hljs.highlight(str, { language: lang }).value}</code></pre>`\n        } catch {}\n      }\n      return `<pre class=\"hljs\"><code class=\"hljs\">${str}</code></pre>`\n    }\n  })\n\n  // Add custom rules for better rendering\n  md.renderer.rules.table_open = () => '<div class=\"table-wrapper\"><table class=\"table\">'\n  md.renderer.rules.table_close = () => '</table></div>'\n  \n  // Custom link rendering with security\n  const defaultLinkRender = md.renderer.rules.link_open || function(tokens, idx, options, _env, self) {\n    return self.renderToken(tokens, idx, options)\n  }\n  \n  md.renderer.rules.link_open = function (tokens, idx, options, env, self) {\n    const token = tokens[idx]\n    const hrefIndex = token.attrIndex('href')\n    \n    if (hrefIndex >= 0) {\n      const href = token.attrs![hrefIndex][1]\n      // Add security attributes for external links\n      if (href.startsWith('http')) {\n        token.attrPush(['target', '_blank'])\n        token.attrPush(['rel', 'noopener noreferrer'])\n      }\n    }\n    \n    return defaultLinkRender(tokens, idx, options, env, self)\n  }\n\n  return md\n}\n\nexport interface MarkdownPreviewRef {\n  getElement: () => HTMLDivElement | null\n}\n\nconst MarkdownPreview = forwardRef<MarkdownPreviewRef, PreviewProps>(({\n  source,\n  className = '',\n  style,\n  ...props\n}, ref) => {\n  const previewRef = React.useRef<HTMLDivElement>(null)\n  \n  // Create markdown renderer instance\n  const markdownRenderer = useMemo(() => createMarkdownRenderer(), [])\n  \n  // Render markdown to HTML\n  const htmlContent = useMemo(() => {\n    if (!source.trim()) {\n      return '<div class=\"text-muted-foreground italic p-4\">Nothing to preview</div>'\n    }\n    \n    try {\n      return markdownRenderer.render(source)\n    } catch (error) {\n      console.error('Markdown rendering error:', error)\n      return '<div class=\"text-destructive p-4\">Error rendering markdown</div>'\n    }\n  }, [source, markdownRenderer])\n\n  // Expose methods via ref\n  React.useImperativeHandle(ref, () => ({\n    getElement: () => previewRef.current,\n  }))\n\n  const previewStyles: React.CSSProperties = {\n    padding: '16px',\n    height: '100%',\n    overflow: 'auto',\n    backgroundColor: 'hsl(var(--background))',\n    color: 'hsl(var(--foreground))',\n    ...style,\n  }\n\n  return (\n    <div\n      ref={previewRef}\n      className={`markdown-preview prose prose-sm dark:prose-invert max-w-none ${className}`}\n      style={previewStyles}\n      dangerouslySetInnerHTML={{ __html: htmlContent }}\n      {...props}\n    />\n  )\n})\n\nMarkdownPreview.displayName = 'MarkdownPreview'\n\nexport { MarkdownPreview }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAOA,qCAAqC;AACrC,MAAM,yBAAyB;IAC7B,MAAM,KAAiB,IAAI,uIAAA,CAAA,UAAU,CAAC;QACpC,MAAM;QACN,SAAS;QACT,aAAa;QACb,WAAW,SAAU,GAAG,EAAE,IAAI;YAC5B,IAAI,QAAQ,8JAAA,CAAA,UAAI,CAAC,WAAW,CAAC,OAAO;gBAClC,IAAI;oBACF,OAAO,CAAC,6CAA6C,EAAE,KAAK,EAAE,EAAE,8JAAA,CAAA,UAAI,CAAC,SAAS,CAAC,KAAK;wBAAE,UAAU;oBAAK,GAAG,KAAK,CAAC,aAAa,CAAC;gBAC9H,EAAE,OAAM,CAAC;YACX;YACA,OAAO,CAAC,qCAAqC,EAAE,IAAI,aAAa,CAAC;QACnE;IACF;IAEA,wCAAwC;IACxC,GAAG,QAAQ,CAAC,KAAK,CAAC,UAAU,GAAG,IAAM;IACrC,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,GAAG,IAAM;IAEtC,sCAAsC;IACtC,MAAM,oBAAoB,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,IAAI,SAAS,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI;QAChG,OAAO,KAAK,WAAW,CAAC,QAAQ,KAAK;IACvC;IAEA,GAAG,QAAQ,CAAC,KAAK,CAAC,SAAS,GAAG,SAAU,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI;QACrE,MAAM,QAAQ,MAAM,CAAC,IAAI;QACzB,MAAM,YAAY,MAAM,SAAS,CAAC;QAElC,IAAI,aAAa,GAAG;YAClB,MAAM,OAAO,MAAM,KAAK,AAAC,CAAC,UAAU,CAAC,EAAE;YACvC,6CAA6C;YAC7C,IAAI,KAAK,UAAU,CAAC,SAAS;gBAC3B,MAAM,QAAQ,CAAC;oBAAC;oBAAU;iBAAS;gBACnC,MAAM,QAAQ,CAAC;oBAAC;oBAAO;iBAAsB;YAC/C;QACF;QAEA,OAAO,kBAAkB,QAAQ,KAAK,SAAS,KAAK;IACtD;IAEA,OAAO;AACT;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAoC,CAAC,EACpE,MAAM,EACN,YAAY,EAAE,EACd,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAEhD,oCAAoC;IACpC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,0BAA0B,EAAE;IAEnE,0BAA0B;IAC1B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,IAAI,CAAC,OAAO,IAAI,IAAI;YAClB,OAAO;QACT;QAEA,IAAI;YACF,OAAO,iBAAiB,MAAM,CAAC;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;QACT;IACF,GAAG;QAAC;QAAQ;KAAiB;IAE7B,yBAAyB;IACzB,qMAAA,CAAA,UAAK,CAAC,mBAAmB,CAAC,KAAK,IAAM,CAAC;YACpC,YAAY,IAAM,WAAW,OAAO;QACtC,CAAC;IAED,MAAM,gBAAqC;QACzC,SAAS;QACT,QAAQ;QACR,UAAU;QACV,iBAAiB;QACjB,OAAO;QACP,GAAG,KAAK;IACV;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,6DAA6D,EAAE,WAAW;QACtF,OAAO;QACP,yBAAyB;YAAE,QAAQ;QAAY;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,gBAAgB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/components/drag-bar.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback, useRef, useEffect } from 'react'\nimport { DragBarProps } from '../types'\n\nexport function DragBar({ \n  height, \n  minHeight, \n  maxHeight, \n  onChange, \n  className = '' \n}: DragBarProps) {\n  const [isDragging, setIsDragging] = useState(false)\n  const [startY, setStartY] = useState(0)\n  const [startHeight, setStartHeight] = useState(height)\n  const dragBarRef = useRef<HTMLDivElement>(null)\n\n  const handleMouseDown = useCallback((event: React.MouseEvent) => {\n    event.preventDefault()\n    setIsDragging(true)\n    setStartY(event.clientY)\n    setStartHeight(height)\n    \n    // Add cursor style to body\n    document.body.style.cursor = 'ns-resize'\n    document.body.style.userSelect = 'none'\n  }, [height])\n\n  const handleMouseMove = useCallback((event: MouseEvent) => {\n    if (!isDragging) return\n    \n    const deltaY = event.clientY - startY\n    const newHeight = Math.max(minHeight, Math.min(maxHeight, startHeight + deltaY))\n    \n    onChange(newHeight)\n  }, [isDragging, startY, startHeight, minHeight, maxHeight, onChange])\n\n  const handleMouseUp = useCallback(() => {\n    setIsDragging(false)\n    \n    // Remove cursor style from body\n    document.body.style.cursor = ''\n    document.body.style.userSelect = ''\n  }, [])\n\n  // Handle touch events for mobile\n  const handleTouchStart = useCallback((event: React.TouchEvent) => {\n    event.preventDefault()\n    const touch = event.touches[0]\n    setIsDragging(true)\n    setStartY(touch.clientY)\n    setStartHeight(height)\n  }, [height])\n\n  const handleTouchMove = useCallback((event: TouchEvent) => {\n    if (!isDragging) return\n    \n    event.preventDefault()\n    const touch = event.touches[0]\n    const deltaY = touch.clientY - startY\n    const newHeight = Math.max(minHeight, Math.min(maxHeight, startHeight + deltaY))\n    \n    onChange(newHeight)\n  }, [isDragging, startY, startHeight, minHeight, maxHeight, onChange])\n\n  const handleTouchEnd = useCallback(() => {\n    setIsDragging(false)\n  }, [])\n\n  // Add global event listeners\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove)\n      document.addEventListener('mouseup', handleMouseUp)\n      document.addEventListener('touchmove', handleTouchMove, { passive: false })\n      document.addEventListener('touchend', handleTouchEnd)\n      \n      return () => {\n        document.removeEventListener('mousemove', handleMouseMove)\n        document.removeEventListener('mouseup', handleMouseUp)\n        document.removeEventListener('touchmove', handleTouchMove)\n        document.removeEventListener('touchend', handleTouchEnd)\n      }\n    }\n  }, [isDragging, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd])\n\n  return (\n    <div\n      ref={dragBarRef}\n      className={`\n        relative h-2 bg-border hover:bg-border/80 cursor-ns-resize\n        flex items-center justify-center group\n        transition-colors duration-200\n        ${isDragging ? 'bg-primary/20' : ''}\n        ${className}\n      `}\n      onMouseDown={handleMouseDown}\n      onTouchStart={handleTouchStart}\n      role=\"separator\"\n      aria-orientation=\"horizontal\"\n      aria-label=\"Resize editor height\"\n      tabIndex={0}\n      onKeyDown={(event) => {\n        if (event.key === 'ArrowUp') {\n          event.preventDefault()\n          const newHeight = Math.max(minHeight, height - 10)\n          onChange(newHeight)\n        } else if (event.key === 'ArrowDown') {\n          event.preventDefault()\n          const newHeight = Math.min(maxHeight, height + 10)\n          onChange(newHeight)\n        }\n      }}\n    >\n      {/* Visual indicator */}\n      <div className=\"flex space-x-1 opacity-60 group-hover:opacity-100 transition-opacity\">\n        <div className=\"w-8 h-0.5 bg-current rounded-full\" />\n        <div className=\"w-8 h-0.5 bg-current rounded-full\" />\n        <div className=\"w-8 h-0.5 bg-current rounded-full\" />\n      </div>\n      \n      {/* Tooltip */}\n      <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap\">\n        Drag to resize • Use arrow keys • Height: {height}px\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKO,SAAS,QAAQ,EACtB,MAAM,EACN,SAAS,EACT,SAAS,EACT,QAAQ,EACR,YAAY,EAAE,EACD;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,MAAM,cAAc;QACpB,cAAc;QACd,UAAU,MAAM,OAAO;QACvB,eAAe;QAEf,2BAA2B;QAC3B,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAC7B,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;IACnC,GAAG;QAAC;KAAO;IAEX,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,CAAC,YAAY;QAEjB,MAAM,SAAS,MAAM,OAAO,GAAG;QAC/B,MAAM,YAAY,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW,cAAc;QAExE,SAAS;IACX,GAAG;QAAC;QAAY;QAAQ;QAAa;QAAW;QAAW;KAAS;IAEpE,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,cAAc;QAEd,gCAAgC;QAChC,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAC7B,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;IACnC,GAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,cAAc;QACpB,MAAM,QAAQ,MAAM,OAAO,CAAC,EAAE;QAC9B,cAAc;QACd,UAAU,MAAM,OAAO;QACvB,eAAe;IACjB,GAAG;QAAC;KAAO;IAEX,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,CAAC,YAAY;QAEjB,MAAM,cAAc;QACpB,MAAM,QAAQ,MAAM,OAAO,CAAC,EAAE;QAC9B,MAAM,SAAS,MAAM,OAAO,GAAG;QAC/B,MAAM,YAAY,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW,cAAc;QAExE,SAAS;IACX,GAAG;QAAC;QAAY;QAAQ;QAAa;QAAW;QAAW;KAAS;IAEpE,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,cAAc;IAChB,GAAG,EAAE;IAEL,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,gBAAgB,CAAC,aAAa,iBAAiB;gBAAE,SAAS;YAAM;YACzE,SAAS,gBAAgB,CAAC,YAAY;YAEtC,OAAO;gBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC1C,SAAS,mBAAmB,CAAC,WAAW;gBACxC,SAAS,mBAAmB,CAAC,aAAa;gBAC1C,SAAS,mBAAmB,CAAC,YAAY;YAC3C;QACF;IACF,GAAG;QAAC;QAAY;QAAiB;QAAe;QAAiB;KAAe;IAEhF,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAC;;;;QAIV,EAAE,aAAa,kBAAkB,GAAG;QACpC,EAAE,UAAU;MACd,CAAC;QACD,aAAa;QACb,cAAc;QACd,MAAK;QACL,oBAAiB;QACjB,cAAW;QACX,UAAU;QACV,WAAW,CAAC;YACV,IAAI,MAAM,GAAG,KAAK,WAAW;gBAC3B,MAAM,cAAc;gBACpB,MAAM,YAAY,KAAK,GAAG,CAAC,WAAW,SAAS;gBAC/C,SAAS;YACX,OAAO,IAAI,MAAM,GAAG,KAAK,aAAa;gBACpC,MAAM,cAAc;gBACpB,MAAM,YAAY,KAAK,GAAG,CAAC,WAAW,SAAS;gBAC/C,SAAS;YACX;QACF;;0BAGA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;oBAAkO;oBACpM;oBAAO;;;;;;;;;;;;;AAI1D", "debugId": null}}, {"offset": {"line": 2176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/hooks/use-theme.ts"], "sourcesContent": ["'use client'\n\nimport { useTheme as useNextTheme } from 'next-themes'\nimport { useEffect, useState } from 'react'\n\nexport interface EditorTheme {\n  theme: 'light' | 'dark' | 'system'\n  resolvedTheme: 'light' | 'dark'\n  setTheme: (theme: 'light' | 'dark' | 'system') => void\n  systemTheme: 'light' | 'dark' | undefined\n}\n\n/**\n * Hook to integrate with the blog's existing theme system\n * Uses next-themes to maintain consistency with the rest of the application\n */\nexport function useEditorTheme(): EditorTheme {\n  const { theme, setTheme, resolvedTheme, systemTheme } = useNextTheme()\n  const [mounted, setMounted] = useState(false)\n\n  // Ensure we're mounted before accessing theme to avoid hydration mismatch\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Return safe defaults during SSR\n  if (!mounted) {\n    return {\n      theme: 'system',\n      resolvedTheme: 'light',\n      setTheme: () => {},\n      systemTheme: undefined,\n    }\n  }\n\n  return {\n    theme: (theme as 'light' | 'dark' | 'system') || 'system',\n    resolvedTheme: (resolvedTheme as 'light' | 'dark') || 'light',\n    setTheme: setTheme as (theme: 'light' | 'dark' | 'system') => void,\n    systemTheme: systemTheme as 'light' | 'dark' | undefined,\n  }\n}\n\n/**\n * Get CSS custom properties for the current theme\n * These match the blog's existing CSS variables\n */\nexport function getThemeVariables(resolvedTheme: 'light' | 'dark') {\n  if (resolvedTheme === 'dark') {\n    return {\n      '--editor-background': '222.2 84% 4.9%', // dark background\n      '--editor-foreground': '210 40% 98%', // dark foreground\n      '--editor-card': '222.2 84% 4.9%', // dark card\n      '--editor-card-foreground': '210 40% 98%', // dark card foreground\n      '--editor-popover': '222.2 84% 4.9%', // dark popover\n      '--editor-popover-foreground': '210 40% 98%', // dark popover foreground\n      '--editor-primary': '210 40% 98%', // dark primary\n      '--editor-primary-foreground': '222.2 84% 4.9%', // dark primary foreground\n      '--editor-secondary': '217.2 32.6% 17.5%', // dark secondary\n      '--editor-secondary-foreground': '210 40% 98%', // dark secondary foreground\n      '--editor-muted': '217.2 32.6% 17.5%', // dark muted\n      '--editor-muted-foreground': '215 20.2% 65.1%', // dark muted foreground\n      '--editor-accent': '217.2 32.6% 17.5%', // dark accent\n      '--editor-accent-foreground': '210 40% 98%', // dark accent foreground\n      '--editor-destructive': '0 62.8% 30.6%', // dark destructive\n      '--editor-destructive-foreground': '210 40% 98%', // dark destructive foreground\n      '--editor-border': '217.2 32.6% 17.5%', // dark border\n      '--editor-input': '217.2 32.6% 17.5%', // dark input\n      '--editor-ring': '212.7 26.8% 83.9%', // dark ring\n    }\n  }\n\n  // Light theme\n  return {\n    '--editor-background': '0 0% 100%', // light background\n    '--editor-foreground': '222.2 84% 4.9%', // light foreground\n    '--editor-card': '0 0% 100%', // light card\n    '--editor-card-foreground': '222.2 84% 4.9%', // light card foreground\n    '--editor-popover': '0 0% 100%', // light popover\n    '--editor-popover-foreground': '222.2 84% 4.9%', // light popover foreground\n    '--editor-primary': '222.2 47.4% 11.2%', // light primary\n    '--editor-primary-foreground': '210 40% 98%', // light primary foreground\n    '--editor-secondary': '210 40% 96%', // light secondary\n    '--editor-secondary-foreground': '222.2 84% 4.9%', // light secondary foreground\n    '--editor-muted': '210 40% 96%', // light muted\n    '--editor-muted-foreground': '215.4 16.3% 46.9%', // light muted foreground\n    '--editor-accent': '210 40% 96%', // light accent\n    '--editor-accent-foreground': '222.2 84% 4.9%', // light accent foreground\n    '--editor-destructive': '0 84.2% 60.2%', // light destructive\n    '--editor-destructive-foreground': '210 40% 98%', // light destructive foreground\n    '--editor-border': '214.3 31.8% 91.4%', // light border\n    '--editor-input': '214.3 31.8% 91.4%', // light input\n    '--editor-ring': '222.2 84% 4.9%', // light ring\n  }\n}\n\n/**\n * Apply theme variables to a DOM element\n */\nexport function applyThemeVariables(element: HTMLElement, resolvedTheme: 'light' | 'dark') {\n  const variables = getThemeVariables(resolvedTheme)\n  \n  Object.entries(variables).forEach(([property, value]) => {\n    element.style.setProperty(property, value)\n  })\n}\n\n/**\n * Get the data-color-mode attribute value for the current theme\n * This matches the blog's existing theme system\n */\nexport function getColorModeAttribute(resolvedTheme: 'light' | 'dark'): 'light' | 'dark' {\n  return resolvedTheme\n}\n\n/**\n * Hook to automatically apply theme variables to the editor container\n */\nexport function useThemeVariables(containerRef: React.RefObject<HTMLElement>) {\n  const { resolvedTheme } = useEditorTheme()\n\n  useEffect(() => {\n    if (containerRef.current) {\n      applyThemeVariables(containerRef.current, resolvedTheme)\n      containerRef.current.setAttribute('data-color-mode', getColorModeAttribute(resolvedTheme))\n    }\n  }, [containerRef, resolvedTheme])\n\n  return { resolvedTheme, colorMode: getColorModeAttribute(resolvedTheme) }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;AAgBO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAY,AAAD;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,0EAA0E;IAC1E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,kCAAkC;IAClC,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,OAAO;YACP,eAAe;YACf,UAAU,KAAO;YACjB,aAAa;QACf;IACF;IAEA,OAAO;QACL,OAAO,AAAC,SAAyC;QACjD,eAAe,AAAC,iBAAsC;QACtD,UAAU;QACV,aAAa;IACf;AACF;AAMO,SAAS,kBAAkB,aAA+B;IAC/D,IAAI,kBAAkB,QAAQ;QAC5B,OAAO;YACL,uBAAuB;YACvB,uBAAuB;YACvB,iBAAiB;YACjB,4BAA4B;YAC5B,oBAAoB;YACpB,+BAA+B;YAC/B,oBAAoB;YACpB,+BAA+B;YAC/B,sBAAsB;YACtB,iCAAiC;YACjC,kBAAkB;YAClB,6BAA6B;YAC7B,mBAAmB;YACnB,8BAA8B;YAC9B,wBAAwB;YACxB,mCAAmC;YACnC,mBAAmB;YACnB,kBAAkB;YAClB,iBAAiB;QACnB;IACF;IAEA,cAAc;IACd,OAAO;QACL,uBAAuB;QACvB,uBAAuB;QACvB,iBAAiB;QACjB,4BAA4B;QAC5B,oBAAoB;QACpB,+BAA+B;QAC/B,oBAAoB;QACpB,+BAA+B;QAC/B,sBAAsB;QACtB,iCAAiC;QACjC,kBAAkB;QAClB,6BAA6B;QAC7B,mBAAmB;QACnB,8BAA8B;QAC9B,wBAAwB;QACxB,mCAAmC;QACnC,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;IACnB;AACF;AAKO,SAAS,oBAAoB,OAAoB,EAAE,aAA+B;IACvF,MAAM,YAAY,kBAAkB;IAEpC,OAAO,OAAO,CAAC,WAAW,OAAO,CAAC,CAAC,CAAC,UAAU,MAAM;QAClD,QAAQ,KAAK,CAAC,WAAW,CAAC,UAAU;IACtC;AACF;AAMO,SAAS,sBAAsB,aAA+B;IACnE,OAAO;AACT;AAKO,SAAS,kBAAkB,YAA0C;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,OAAO,EAAE;YACxB,oBAAoB,aAAa,OAAO,EAAE;YAC1C,aAAa,OAAO,CAAC,YAAY,CAAC,mBAAmB,sBAAsB;QAC7E;IACF,GAAG;QAAC;QAAc;KAAc;IAEhC,OAAO;QAAE;QAAe,WAAW,sBAAsB;IAAe;AAC1E", "debugId": null}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/markdown-editor.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useRef, useCallback, useImperativeHandle, forwardRef, useState } from 'react'\nimport { createPortal } from 'react-dom'\nimport { MarkdownEditorProps, EditorState, Command } from './types'\nimport { EditorProvider, useEditor, closeAllPopups } from './context'\nimport { Textarea, TextareaRef } from './components/textarea'\nimport { Toolbar } from './components/toolbar'\nimport { MarkdownPreview, MarkdownPreviewRef } from './components/preview'\nimport { DragBar } from './components/drag-bar'\nimport { CommandOrchestrator } from './utils/text-api'\nimport { useThemeVariables } from './hooks/use-theme'\n\nexport interface MarkdownEditorRef {\n  focus: () => void\n  blur: () => void\n  getState: () => EditorState\n  getTextArea: () => HTMLTextAreaElement | null\n}\n\n// Internal editor component that uses context\nconst InternalMarkdownEditor = forwardRef<MarkdownEditorRef, MarkdownEditorProps>(({\n  value = '',\n  onChange,\n  onHeightChange,\n  height = 400,\n  minHeight = 200,\n  maxHeight = 800,\n  autoFocus = false,\n  preview = 'live',\n  fullscreen = false,\n  overflow = true,\n  visibleDragbar = true,\n  hideToolbar = false,\n  toolbarBottom = false,\n  enableScroll = true,\n  highlightEnable = true,\n  tabSize = 2,\n  defaultTabEnable = false,\n  commands,\n  extraCommands,\n  commandsFilter,\n  className = '',\n  'data-color-mode': dataColorMode = 'auto',\n  components,\n  textareaProps,\n  previewOptions,\n  ...props\n}, ref) => {\n  const { state, dispatch } = useEditor()\n  const containerRef = useRef<HTMLDivElement>(null)\n  const textareaRef = useRef<TextareaRef>(null)\n  const previewRef = useRef<MarkdownPreviewRef>(null)\n  const orchestratorRef = useRef<CommandOrchestrator | null>(null)\n  const [isMounted, setIsMounted] = useState(false)\n\n  useEffect(() => {\n    setIsMounted(true)\n  }, [])\n\n  // Destructure value from textareaProps to avoid type conflict\n  const { value: textareaValue, ...restTextareaProps } = textareaProps || {};\n  // Suppress unused variable warning since this is intentionally extracted to avoid conflicts\n  void textareaValue;\n\n  // Theme integration\n  const { resolvedTheme } = useThemeVariables(containerRef as React.RefObject<HTMLElement>)\n\n  // Expose methods via ref\n  useImperativeHandle(ref, () => ({\n    focus: () => textareaRef.current?.focus(),\n    blur: () => textareaRef.current?.blur(),\n    getState: () => state,\n    getTextArea: () => textareaRef.current?.getTextArea() || null,\n  }))\n\n  // Initialize editor state\n  useEffect(() => {\n    dispatch({\n      markdown: value,\n      preview,\n      fullscreen,\n      height,\n      highlightEnable,\n      tabSize,\n      defaultTabEnable,\n      commands: commands || state.commands,\n      extraCommands: extraCommands || state.extraCommands,\n      container: containerRef.current,\n    })\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []) // Only run on mount - intentionally ignoring dependencies\n\n  // Update state when props change\n  useEffect(() => {\n    if (value !== state.markdown) {\n      dispatch({ markdown: value })\n    }\n  }, [value, state.markdown, dispatch])\n\n  useEffect(() => {\n    if (preview !== state.preview) {\n      dispatch({ preview })\n    }\n  }, [preview, state.preview, dispatch])\n\n  useEffect(() => {\n    if (fullscreen !== state.fullscreen) {\n      dispatch({ fullscreen })\n    }\n  }, [fullscreen, state.fullscreen, dispatch])\n\n  // Handle browser fullscreen API\n  useEffect(() => {\n    if (typeof window === 'undefined') return; // Ensure this runs only on the client\n\n    const container = containerRef.current;\n    if (!container) return;\n\n    // Check if Fullscreen API is supported\n    const doc = document as Document & {\n      fullscreenEnabled?: boolean;\n      webkitFullscreenEnabled?: boolean;\n      mozFullScreenEnabled?: boolean;\n      msFullscreenEnabled?: boolean;\n      webkitFullscreenElement?: Element;\n      mozFullScreenElement?: Element;\n      msFullscreenElement?: Element;\n      webkitExitFullscreen?: () => Promise<void>;\n      mozCancelFullScreen?: () => Promise<void>;\n      msExitFullscreen?: () => Promise<void>;\n    };\n    const fullscreenEnabled = doc.fullscreenEnabled ||\n      doc.webkitFullscreenEnabled ||\n      doc.mozFullScreenEnabled ||\n      doc.msFullscreenEnabled;\n\n    if (!fullscreenEnabled) {\n      console.warn('Fullscreen API is not supported in this browser');\n      return;\n    }\n\n    // Get the appropriate fullscreen element property\n    const getFullscreenElement = () => {\n      return doc.fullscreenElement ||\n        doc.webkitFullscreenElement ||\n        doc.mozFullScreenElement ||\n        doc.msFullscreenElement;\n    };\n\n    // Handle fullscreen change\n    const handleFullscreenChange = () => {\n      const isFullscreen = Boolean(getFullscreenElement());\n      if (isFullscreen !== state.fullscreen) {\n        dispatch({ fullscreen: isFullscreen });\n      }\n    };\n\n    // Add event listeners for all browser variants\n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);\n    document.addEventListener('mozfullscreenchange', handleFullscreenChange);\n    document.addEventListener('MSFullscreenChange', handleFullscreenChange);\n\n    // Handle fullscreen state changes\n    if (state.fullscreen) {\n      if (!getFullscreenElement()) {\n        // Request fullscreen with fallbacks\n        const containerElement = container as HTMLElement & {\n          webkitRequestFullscreen?: () => Promise<void>;\n          mozRequestFullScreen?: () => Promise<void>;\n          msRequestFullscreen?: () => Promise<void>;\n        };\n        const requestFullscreen = containerElement.requestFullscreen ||\n          containerElement.webkitRequestFullscreen ||\n          containerElement.mozRequestFullScreen ||\n          containerElement.msRequestFullscreen;\n\n        requestFullscreen.call(container).catch((err: Error) => {\n          console.error(`Error attempting to enable fullscreen mode: ${err.message}`);\n          dispatch({ fullscreen: false });\n        });\n      }\n    } else {\n      if (getFullscreenElement()) {\n        // Exit fullscreen with fallbacks\n        const exitFullscreen = doc.exitFullscreen ||\n          doc.webkitExitFullscreen ||\n          doc.mozCancelFullScreen ||\n          doc.msExitFullscreen;\n\n        exitFullscreen.call(document).catch((err: Error) => {\n          console.error(`Error attempting to exit fullscreen mode: ${err.message}`);\n          dispatch({ fullscreen: true });\n        });\n      }\n    }\n\n    // Cleanup event listeners\n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);\n      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);\n      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);\n    };\n  }, [state.fullscreen, dispatch]);\n\n  useEffect(() => {\n    if (height !== state.height) {\n      dispatch({ height })\n      onHeightChange?.(height, state.height, state)\n    }\n  }, [height, state.height, dispatch, onHeightChange, state])\n\n  // Handle textarea change\n  const handleTextareaChange = useCallback((event: React.ChangeEvent<HTMLTextAreaElement>) => {\n    const newValue = event.target.value\n    dispatch({ markdown: newValue })\n    onChange?.(newValue, event, state)\n    textareaProps?.onChange?.(event)\n  }, [onChange, state, textareaProps, dispatch])\n\n  // Execute command\n  const executeCommand = useCallback((command: Command) => {\n    if (!command.execute) return\n    \n    const textarea = textareaRef.current?.getTextArea()\n    if (!textarea) return\n\n    if (!orchestratorRef.current) {\n      orchestratorRef.current = new CommandOrchestrator(textarea)\n    }\n\n    const textApi = orchestratorRef.current.getTextApi()\n    const textState = orchestratorRef.current.getState()\n    \n    if (textState) {\n      command.execute(textState, textApi, dispatch, {\n        markdown: state.markdown,\n        preview: state.preview,\n        fullscreen: state.fullscreen,\n        height: state.height,\n      })\n    }\n  }, [state, dispatch])\n\n  // Handle container click to close popups\n  const handleContainerClick = useCallback(() => {\n    dispatch({ barPopup: closeAllPopups(state.barPopup) })\n  }, [state.barPopup, dispatch])\n\n  // Handle scroll synchronization\n  const handleTextareaScroll = useCallback((event: React.UIEvent<HTMLTextAreaElement>) => {\n    if (!enableScroll) return\n    \n    const textarea = event.currentTarget\n    const previewElement = previewRef.current?.getElement()\n    \n    if (previewElement && textarea) {\n      const scrollPercentage = textarea.scrollTop / (textarea.scrollHeight - textarea.clientHeight)\n      const previewScrollTop = scrollPercentage * (previewElement.scrollHeight - previewElement.clientHeight)\n      previewElement.scrollTop = previewScrollTop\n    }\n  }, [enableScroll])\n\n\n\n  // Handle drag bar change\n  const handleDragBarChange = useCallback((newHeight: number) => {\n    dispatch({ height: newHeight })\n    onHeightChange?.(newHeight, state.height, state)\n  }, [dispatch, onHeightChange, state])\n\n  // Filter commands if needed\n  const filteredCommands = commands || state.commands.filter(cmd => \n    commandsFilter ? commandsFilter(cmd, false) !== false : true\n  )\n  const filteredExtraCommands = extraCommands || state.extraCommands.filter(cmd => \n    commandsFilter ? commandsFilter(cmd, true) !== false : true\n  )\n\n  // Container classes\n  const containerClasses = [\n    'markdown-editor',\n    'border border-border rounded-lg overflow-hidden',\n    'bg-background text-foreground',\n    'flex flex-col',\n    'w-full max-w-[90%] mx-auto',\n    state.fullscreen ? 'fixed inset-0 z-50 fullscreen' : 'relative',\n    state.preview === 'edit' ? 'editor-only' : '',\n    state.preview === 'preview' ? 'preview-only' : '',\n    state.preview === 'live' ? 'editor-live' : '',\n    // Mobile optimizations\n    'sm:rounded-lg', // Only rounded on small screens and up\n    className,\n  ].filter(Boolean).join(' ')\n\n  const editorHeight = typeof state.height === 'number' ? `${state.height}px` : state.height\n\n  const editorMarkup = (\n    <div\n      ref={containerRef}\n      className={containerClasses}\n      style={state.fullscreen ? {} : { height: editorHeight }} /* Remove height for fullscreen, let CSS handle it */\n      data-color-mode={dataColorMode || 'auto'}\n      data-theme={resolvedTheme}\n      onClick={handleContainerClick}\n      {...props}\n    >\n      {/* Toolbar */}\n      {!hideToolbar && !toolbarBottom && (\n        <>\n          <Toolbar\n            commands={filteredCommands}\n            extraCommands={filteredExtraCommands}\n            executeCommand={executeCommand}\n            overflow={overflow}\n            toolbarBottom={false}\n            state={state}\n            dispatch={dispatch}\n          />\n          {/* Hidden text for SSR hydration */}\n          <div style={{display: 'none'}}>\n            Title 1 Title 2 Title 3 Title 4 Title 5 Title 6\n          </div>\n        </>\n      )}\n\n      {/* Editor content */}\n      <div className={`\n        flex-1 flex overflow-hidden editor-content\n        ${state.preview === 'live' ? 'md:flex-row flex-col' : 'flex-row'}\n      `}>\n        {/* Editor pane */}\n        {(state.preview === 'edit' || state.preview === 'live') && (\n          <div className={`\n            ${state.preview === 'live'\n              ? 'md:w-[60%] w-full md:h-full h-1/2'\n              : 'w-full h-full'\n            }\n            relative editor-pane\n          `}>\n            {components?.textarea ? (\n              components.textarea({\n                value: state.markdown || '',\n                onChange: handleTextareaChange,\n                onScroll: handleTextareaScroll,\n                highlightEnable,\n                tabSize,\n                defaultTabEnable,\n                autoFocus,\n                ...restTextareaProps, // Use restTextareaProps here\n              })\n            ) : (\n              <Textarea\n                ref={textareaRef}\n                value={state.markdown || ''}\n                onChange={handleTextareaChange}\n                onScroll={handleTextareaScroll}\n                highlightEnable={highlightEnable}\n                tabSize={tabSize}\n                defaultTabEnable={defaultTabEnable}\n                autoFocus={autoFocus}\n                {...restTextareaProps} // Use restTextareaProps here\n              />\n            )}\n          </div>\n        )}\n\n        {/* Divider for live mode */}\n        {state.preview === 'live' && (\n          <div className=\"md:w-px md:h-auto w-full h-px bg-border editor-divider\" />\n        )}\n\n        {/* Preview pane */}\n        {(state.preview === 'preview' || state.preview === 'live') && (\n          <div className={`\n            ${state.preview === 'live'\n              ? 'md:w-[40%] w-full md:h-full h-1/2'\n              : 'w-full h-full'\n            }\n            relative preview-pane\n          `}>\n            {components?.preview ? (\n              components.preview(state.markdown || '', state, dispatch)\n            ) : (\n              <MarkdownPreview\n                ref={previewRef}\n                source={state.markdown || ''}\n                {...previewOptions}\n              />\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Drag bar */}\n      {visibleDragbar && !state.fullscreen && (\n        <DragBar\n          height={typeof state.height === 'number' ? state.height : 400}\n          minHeight={minHeight}\n          maxHeight={maxHeight}\n          onChange={handleDragBarChange}\n        />\n      )}\n\n      {/* Bottom toolbar */}\n      {!hideToolbar && toolbarBottom && (\n        <Toolbar\n          commands={filteredCommands}\n          extraCommands={filteredExtraCommands}\n          executeCommand={executeCommand}\n          overflow={overflow}\n          toolbarBottom={true}\n          state={state}\n          dispatch={dispatch}\n        />\n      )}\n    </div>\n  );\n\n  if (!isMounted) {\n    return editorMarkup;\n  }\n\n  if (state.fullscreen) {\n    return createPortal(editorMarkup, document.body);\n  }\n\n  return editorMarkup;\n})\n\nInternalMarkdownEditor.displayName = 'InternalMarkdownEditor'\n\n// Main editor component with provider\nconst MarkdownEditor = forwardRef<MarkdownEditorRef, MarkdownEditorProps>((props, ref) => {\n  return (\n    <EditorProvider>\n      <InternalMarkdownEditor ref={ref} {...props} />\n    </EditorProvider>\n  )\n})\n\nMarkdownEditor.displayName = 'MarkdownEditor'\n\nexport { MarkdownEditor }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAoBA,8CAA8C;AAC9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAA0C,CAAC,EACjF,QAAQ,EAAE,EACV,QAAQ,EACR,cAAc,EACd,SAAS,GAAG,EACZ,YAAY,GAAG,EACf,YAAY,GAAG,EACf,YAAY,KAAK,EACjB,UAAU,MAAM,EAChB,aAAa,KAAK,EAClB,WAAW,IAAI,EACf,iBAAiB,IAAI,EACrB,cAAc,KAAK,EACnB,gBAAgB,KAAK,EACrB,eAAe,IAAI,EACnB,kBAAkB,IAAI,EACtB,UAAU,CAAC,EACX,mBAAmB,KAAK,EACxB,QAAQ,EACR,aAAa,EACb,cAAc,EACd,YAAY,EAAE,EACd,mBAAmB,gBAAgB,MAAM,EACzC,UAAU,EACV,aAAa,EACb,cAAc,EACd,GAAG,OACJ,EAAE;IACD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD;IACpC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACxC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAC9C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA8B;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,8DAA8D;IAC9D,MAAM,EAAE,OAAO,aAAa,EAAE,GAAG,mBAAmB,GAAG,iBAAiB,CAAC;IACzE,4FAA4F;IAC5F,KAAK;IAEL,oBAAoB;IACpB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE;IAE5C,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAM,CAAC;YAC9B,OAAO,IAAM,YAAY,OAAO,EAAE;YAClC,MAAM,IAAM,YAAY,OAAO,EAAE;YACjC,UAAU,IAAM;YAChB,aAAa,IAAM,YAAY,OAAO,EAAE,iBAAiB;QAC3D,CAAC;IAED,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YACP,UAAU;YACV;YACA;YACA;YACA;YACA;YACA;YACA,UAAU,YAAY,MAAM,QAAQ;YACpC,eAAe,iBAAiB,MAAM,aAAa;YACnD,WAAW,aAAa,OAAO;QACjC;IACF,uDAAuD;IACvD,GAAG,EAAE,EAAE,0DAA0D;;IAEjE,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,MAAM,QAAQ,EAAE;YAC5B,SAAS;gBAAE,UAAU;YAAM;QAC7B;IACF,GAAG;QAAC;QAAO,MAAM,QAAQ;QAAE;KAAS;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,MAAM,OAAO,EAAE;YAC7B,SAAS;gBAAE;YAAQ;QACrB;IACF,GAAG;QAAC;QAAS,MAAM,OAAO;QAAE;KAAS;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,MAAM,UAAU,EAAE;YACnC,SAAS;gBAAE;YAAW;QACxB;IACF,GAAG;QAAC;QAAY,MAAM,UAAU;QAAE;KAAS;IAE3C,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC,QAAQ,sCAAsC;;QAEjF,MAAM;QAGN,uCAAuC;QACvC,MAAM;QAYN,MAAM;QAUN,kDAAkD;QAClD,MAAM;QAON,2BAA2B;QAC3B,MAAM;IAsDR,GAAG;QAAC,MAAM,UAAU;QAAE;KAAS;IAE/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,MAAM,MAAM,EAAE;YAC3B,SAAS;gBAAE;YAAO;YAClB,iBAAiB,QAAQ,MAAM,MAAM,EAAE;QACzC;IACF,GAAG;QAAC;QAAQ,MAAM,MAAM;QAAE;QAAU;QAAgB;KAAM;IAE1D,yBAAyB;IACzB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,MAAM,WAAW,MAAM,MAAM,CAAC,KAAK;QACnC,SAAS;YAAE,UAAU;QAAS;QAC9B,WAAW,UAAU,OAAO;QAC5B,eAAe,WAAW;IAC5B,GAAG;QAAC;QAAU;QAAO;QAAe;KAAS;IAE7C,kBAAkB;IAClB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,MAAM,WAAW,YAAY,OAAO,EAAE;QACtC,IAAI,CAAC,UAAU;QAEf,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,gBAAgB,OAAO,GAAG,IAAI,+JAAA,CAAA,sBAAmB,CAAC;QACpD;QAEA,MAAM,UAAU,gBAAgB,OAAO,CAAC,UAAU;QAClD,MAAM,YAAY,gBAAgB,OAAO,CAAC,QAAQ;QAElD,IAAI,WAAW;YACb,QAAQ,OAAO,CAAC,WAAW,SAAS,UAAU;gBAC5C,UAAU,MAAM,QAAQ;gBACxB,SAAS,MAAM,OAAO;gBACtB,YAAY,MAAM,UAAU;gBAC5B,QAAQ,MAAM,MAAM;YACtB;QACF;IACF,GAAG;QAAC;QAAO;KAAS;IAEpB,yCAAyC;IACzC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,SAAS;YAAE,UAAU,CAAA,GAAA,mJAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;QAAE;IACtD,GAAG;QAAC,MAAM,QAAQ;QAAE;KAAS;IAE7B,gCAAgC;IAChC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,IAAI,CAAC,cAAc;QAEnB,MAAM,WAAW,MAAM,aAAa;QACpC,MAAM,iBAAiB,WAAW,OAAO,EAAE;QAE3C,IAAI,kBAAkB,UAAU;YAC9B,MAAM,mBAAmB,SAAS,SAAS,GAAG,CAAC,SAAS,YAAY,GAAG,SAAS,YAAY;YAC5F,MAAM,mBAAmB,mBAAmB,CAAC,eAAe,YAAY,GAAG,eAAe,YAAY;YACtG,eAAe,SAAS,GAAG;QAC7B;IACF,GAAG;QAAC;KAAa;IAIjB,yBAAyB;IACzB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,SAAS;YAAE,QAAQ;QAAU;QAC7B,iBAAiB,WAAW,MAAM,MAAM,EAAE;IAC5C,GAAG;QAAC;QAAU;QAAgB;KAAM;IAEpC,4BAA4B;IAC5B,MAAM,mBAAmB,YAAY,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,MACzD,iBAAiB,eAAe,KAAK,WAAW,QAAQ;IAE1D,MAAM,wBAAwB,iBAAiB,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,MACxE,iBAAiB,eAAe,KAAK,UAAU,QAAQ;IAGzD,oBAAoB;IACpB,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA,MAAM,UAAU,GAAG,kCAAkC;QACrD,MAAM,OAAO,KAAK,SAAS,gBAAgB;QAC3C,MAAM,OAAO,KAAK,YAAY,iBAAiB;QAC/C,MAAM,OAAO,KAAK,SAAS,gBAAgB;QAC3C,uBAAuB;QACvB;QACA;KACD,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,MAAM,eAAe,OAAO,MAAM,MAAM,KAAK,WAAW,GAAG,MAAM,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,MAAM;IAE1F,MAAM,6BACJ,8OAAC;QACC,KAAK;QACL,WAAW;QACX,OAAO,MAAM,UAAU,GAAG,CAAC,IAAI;YAAE,QAAQ;QAAa;QACtD,mBAAiB,iBAAiB;QAClC,cAAY;QACZ,SAAS;QACR,GAAG,KAAK;;YAGR,CAAC,eAAe,CAAC,+BAChB;;kCACE,8OAAC,iKAAA,CAAA,UAAO;wBACN,UAAU;wBACV,eAAe;wBACf,gBAAgB;wBAChB,UAAU;wBACV,eAAe;wBACf,OAAO;wBACP,UAAU;;;;;;kCAGZ,8OAAC;wBAAI,OAAO;4BAAC,SAAS;wBAAM;kCAAG;;;;;;;;0BAOnC,8OAAC;gBAAI,WAAW,CAAC;;QAEf,EAAE,MAAM,OAAO,KAAK,SAAS,yBAAyB,WAAW;MACnE,CAAC;;oBAEE,CAAC,MAAM,OAAO,KAAK,UAAU,MAAM,OAAO,KAAK,MAAM,mBACpD,8OAAC;wBAAI,WAAW,CAAC;YACf,EAAE,MAAM,OAAO,KAAK,SAChB,sCACA,gBACH;;UAEH,CAAC;kCACE,YAAY,WACX,WAAW,QAAQ,CAAC;4BAClB,OAAO,MAAM,QAAQ,IAAI;4BACzB,UAAU;4BACV,UAAU;4BACV;4BACA;4BACA;4BACA;4BACA,GAAG,iBAAiB;wBACtB,mBAEA,8OAAC,kKAAA,CAAA,WAAQ;4BACP,KAAK;4BACL,OAAO,MAAM,QAAQ,IAAI;4BACzB,UAAU;4BACV,UAAU;4BACV,iBAAiB;4BACjB,SAAS;4BACT,kBAAkB;4BAClB,WAAW;4BACV,GAAG,iBAAiB;;;;;;;;;;;oBAO5B,MAAM,OAAO,KAAK,wBACjB,8OAAC;wBAAI,WAAU;;;;;;oBAIhB,CAAC,MAAM,OAAO,KAAK,aAAa,MAAM,OAAO,KAAK,MAAM,mBACvD,8OAAC;wBAAI,WAAW,CAAC;YACf,EAAE,MAAM,OAAO,KAAK,SAChB,sCACA,gBACH;;UAEH,CAAC;kCACE,YAAY,UACX,WAAW,OAAO,CAAC,MAAM,QAAQ,IAAI,IAAI,OAAO,0BAEhD,8OAAC,iKAAA,CAAA,kBAAe;4BACd,KAAK;4BACL,QAAQ,MAAM,QAAQ,IAAI;4BACzB,GAAG,cAAc;;;;;;;;;;;;;;;;;YAQ3B,kBAAkB,CAAC,MAAM,UAAU,kBAClC,8OAAC,qKAAA,CAAA,UAAO;gBACN,QAAQ,OAAO,MAAM,MAAM,KAAK,WAAW,MAAM,MAAM,GAAG;gBAC1D,WAAW;gBACX,WAAW;gBACX,UAAU;;;;;;YAKb,CAAC,eAAe,+BACf,8OAAC,iKAAA,CAAA,UAAO;gBACN,UAAU;gBACV,eAAe;gBACf,gBAAgB;gBAChB,UAAU;gBACV,eAAe;gBACf,OAAO;gBACP,UAAU;;;;;;;;;;;;IAMlB,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,IAAI,MAAM,UAAU,EAAE;QACpB,qBAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI;IACjD;IAEA,OAAO;AACT;AAEA,uBAAuB,WAAW,GAAG;AAErC,sCAAsC;AACtC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAA0C,CAAC,OAAO;IAChF,qBACE,8OAAC,mJAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC;YAAuB,KAAK;YAAM,GAAG,KAAK;;;;;;;;;;;AAGjD;AAEA,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/utils/markdown.ts"], "sourcesContent": ["/**\n * Markdown processing utilities\n */\n\n/**\n * Extract frontmatter from markdown content\n */\nexport function extractFrontmatter(markdown: string): {\n  frontmatter: Record<string, unknown>\n  content: string\n} {\n  const frontmatterRegex = /^---\\s*\\n([\\s\\S]*?)\\n---\\s*\\n([\\s\\S]*)$/\n  const match = markdown.match(frontmatterRegex)\n  \n  if (!match) {\n    return { frontmatter: {}, content: markdown }\n  }\n  \n  const [, frontmatterStr, content] = match\n  \n  try {\n    // Simple YAML-like parsing for basic frontmatter\n    const frontmatter: Record<string, unknown> = {}\n    frontmatterStr.split('\\n').forEach(line => {\n      const colonIndex = line.indexOf(':')\n      if (colonIndex > 0) {\n        const key = line.substring(0, colonIndex).trim()\n        const value = line.substring(colonIndex + 1).trim()\n        \n        // Remove quotes if present\n        const cleanValue = value.replace(/^[\"']|[\"']$/g, '')\n        \n        // Try to parse as number or boolean\n        if (cleanValue === 'true') {\n          frontmatter[key] = true\n        } else if (cleanValue === 'false') {\n          frontmatter[key] = false\n        } else if (!isNaN(Number(cleanValue)) && cleanValue !== '') {\n          frontmatter[key] = Number(cleanValue)\n        } else {\n          frontmatter[key] = cleanValue\n        }\n      }\n    })\n    \n    return { frontmatter, content }\n  } catch (error) {\n    console.warn('Failed to parse frontmatter:', error)\n    return { frontmatter: {}, content: markdown }\n  }\n}\n\n/**\n * Add or update frontmatter in markdown content\n */\nexport function updateFrontmatter(\n  markdown: string,\n  updates: Record<string, unknown>\n): string {\n  const { frontmatter, content } = extractFrontmatter(markdown)\n  const updatedFrontmatter = { ...frontmatter, ...updates }\n  \n  if (Object.keys(updatedFrontmatter).length === 0) {\n    return content\n  }\n  \n  const frontmatterStr = Object.entries(updatedFrontmatter)\n    .map(([key, value]) => {\n      if (typeof value === 'string') {\n        return `${key}: \"${value}\"`\n      }\n      return `${key}: ${value}`\n    })\n    .join('\\n')\n  \n  return `---\\n${frontmatterStr}\\n---\\n${content}`\n}\n\n/**\n * Extract headings from markdown content\n */\nexport function extractHeadings(markdown: string): Array<{\n  level: number\n  text: string\n  id: string\n  line: number\n}> {\n  const lines = markdown.split('\\n')\n  const headings: Array<{ level: number; text: string; id: string; line: number }> = []\n  \n  lines.forEach((line, index) => {\n    const match = line.match(/^(#{1,6})\\s+(.+)$/)\n    if (match) {\n      const level = match[1].length\n      const text = match[2].trim()\n      const id = text\n        .toLowerCase()\n        .replace(/[^\\w\\s-]/g, '')\n        .replace(/\\s+/g, '-')\n        .replace(/-+/g, '-')\n        .replace(/^-|-$/g, '')\n      \n      headings.push({\n        level,\n        text,\n        id,\n        line: index + 1,\n      })\n    }\n  })\n  \n  return headings\n}\n\n/**\n * Generate table of contents from markdown\n */\nexport function generateTableOfContents(markdown: string): string {\n  const headings = extractHeadings(markdown)\n  \n  if (headings.length === 0) {\n    return ''\n  }\n  \n  const toc = headings\n    .map(heading => {\n      const indent = '  '.repeat(heading.level - 1)\n      return `${indent}- [${heading.text}](#${heading.id})`\n    })\n    .join('\\n')\n  \n  return `## Table of Contents\\n\\n${toc}\\n`\n}\n\n/**\n * Count words, characters, and other statistics\n */\nexport function getMarkdownStats(markdown: string): {\n  words: number\n  characters: number\n  charactersNoSpaces: number\n  paragraphs: number\n  headings: number\n  links: number\n  images: number\n  codeBlocks: number\n  lists: number\n  readingTime: number // in minutes\n} {\n  const { content } = extractFrontmatter(markdown)\n  \n  // Remove code blocks for word counting\n  const withoutCodeBlocks = content.replace(/```[\\s\\S]*?```/g, '')\n  \n  // Remove inline code for word counting\n  const withoutInlineCode = withoutCodeBlocks.replace(/`[^`]+`/g, '')\n  \n  // Count words (split by whitespace and filter empty strings)\n  const words = withoutInlineCode\n    .trim()\n    .split(/\\s+/)\n    .filter(word => word.length > 0).length\n  \n  const characters = content.length\n  const charactersNoSpaces = content.replace(/\\s/g, '').length\n  \n  // Count paragraphs (non-empty lines that aren't headings, lists, etc.)\n  const paragraphs = content\n    .split('\\n')\n    .filter(line => {\n      const trimmed = line.trim()\n      return trimmed.length > 0 && \n             !trimmed.startsWith('#') && \n             !trimmed.startsWith('-') && \n             !trimmed.startsWith('*') && \n             !trimmed.startsWith('+') && \n             !trimmed.match(/^\\d+\\./) &&\n             !trimmed.startsWith('>')\n    }).length\n  \n  const headings = (content.match(/^#{1,6}\\s+.+$/gm) || []).length\n  const links = (content.match(/\\[([^\\]]+)\\]\\(([^)]+)\\)/g) || []).length\n  const images = (content.match(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g) || []).length\n  const codeBlocks = (content.match(/```[\\s\\S]*?```/g) || []).length\n  const lists = (content.match(/^[\\s]*[-*+]\\s+/gm) || []).length + \n                (content.match(/^[\\s]*\\d+\\.\\s+/gm) || []).length\n  \n  // Estimate reading time (average 200 words per minute)\n  const readingTime = Math.ceil(words / 200)\n  \n  return {\n    words,\n    characters,\n    charactersNoSpaces,\n    paragraphs,\n    headings,\n    links,\n    images,\n    codeBlocks,\n    lists,\n    readingTime,\n  }\n}\n\n/**\n * Validate markdown syntax and return errors\n */\nexport function validateMarkdown(markdown: string): Array<{\n  line: number\n  column: number\n  message: string\n  type: 'error' | 'warning'\n}> {\n  const errors: Array<{ line: number; column: number; message: string; type: 'error' | 'warning' }> = []\n  const lines = markdown.split('\\n')\n  \n  lines.forEach((line, index) => {\n    const lineNumber = index + 1\n    \n    // Check for unmatched brackets in links\n    const linkMatches = line.match(/\\[([^\\]]*)\\]/g)\n    if (linkMatches) {\n      linkMatches.forEach(match => {\n        const column = line.indexOf(match) + 1\n        if (!line.includes(`${match}(`)) {\n          errors.push({\n            line: lineNumber,\n            column,\n            message: 'Link text without URL',\n            type: 'warning',\n          })\n        }\n      })\n    }\n    \n    // Check for unmatched code blocks\n    const codeBlockMatches = line.match(/```/g)\n    if (codeBlockMatches && codeBlockMatches.length % 2 !== 0) {\n      // This is a simple check - a more sophisticated parser would track state\n      const column = line.indexOf('```') + 1\n      errors.push({\n        line: lineNumber,\n        column,\n        message: 'Potentially unmatched code block',\n        type: 'warning',\n      })\n    }\n    \n    // Check for empty headings\n    if (line.match(/^#{1,6}\\s*$/)) {\n      errors.push({\n        line: lineNumber,\n        column: 1,\n        message: 'Empty heading',\n        type: 'warning',\n      })\n    }\n  })\n  \n  return errors\n}\n\n/**\n * Clean up markdown formatting\n */\nexport function cleanMarkdown(markdown: string): string {\n  return markdown\n    // Remove excessive blank lines\n    .replace(/\\n{3,}/g, '\\n\\n')\n    // Trim whitespace from lines\n    .split('\\n')\n    .map(line => line.trimEnd())\n    .join('\\n')\n    // Remove trailing whitespace\n    .trim()\n}\n\n/**\n * Convert markdown to plain text\n */\nexport function markdownToPlainText(markdown: string): string {\n  const { content } = extractFrontmatter(markdown)\n  \n  return content\n    // Remove code blocks\n    .replace(/```[\\s\\S]*?```/g, '')\n    // Remove inline code\n    .replace(/`[^`]+`/g, '')\n    // Remove images\n    .replace(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g, '$1')\n    // Remove links but keep text\n    .replace(/\\[([^\\]]+)\\]\\(([^)]+)\\)/g, '$1')\n    // Remove headings markers\n    .replace(/^#{1,6}\\s+/gm, '')\n    // Remove bold/italic\n    .replace(/\\*\\*([^*]+)\\*\\*/g, '$1')\n    .replace(/\\*([^*]+)\\*/g, '$1')\n    // Remove strikethrough\n    .replace(/~~([^~]+)~~/g, '$1')\n    // Remove blockquotes\n    .replace(/^>\\s+/gm, '')\n    // Remove list markers\n    .replace(/^[\\s]*[-*+]\\s+/gm, '')\n    .replace(/^[\\s]*\\d+\\.\\s+/gm, '')\n    // Remove horizontal rules\n    .replace(/^---+$/gm, '')\n    // Clean up whitespace\n    .replace(/\\n{2,}/g, '\\n\\n')\n    .trim()\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;CAEC;;;;;;;;;;AACM,SAAS,mBAAmB,QAAgB;IAIjD,MAAM,mBAAmB;IACzB,MAAM,QAAQ,SAAS,KAAK,CAAC;IAE7B,IAAI,CAAC,OAAO;QACV,OAAO;YAAE,aAAa,CAAC;YAAG,SAAS;QAAS;IAC9C;IAEA,MAAM,GAAG,gBAAgB,QAAQ,GAAG;IAEpC,IAAI;QACF,iDAAiD;QACjD,MAAM,cAAuC,CAAC;QAC9C,eAAe,KAAK,CAAC,MAAM,OAAO,CAAC,CAAA;YACjC,MAAM,aAAa,KAAK,OAAO,CAAC;YAChC,IAAI,aAAa,GAAG;gBAClB,MAAM,MAAM,KAAK,SAAS,CAAC,GAAG,YAAY,IAAI;gBAC9C,MAAM,QAAQ,KAAK,SAAS,CAAC,aAAa,GAAG,IAAI;gBAEjD,2BAA2B;gBAC3B,MAAM,aAAa,MAAM,OAAO,CAAC,gBAAgB;gBAEjD,oCAAoC;gBACpC,IAAI,eAAe,QAAQ;oBACzB,WAAW,CAAC,IAAI,GAAG;gBACrB,OAAO,IAAI,eAAe,SAAS;oBACjC,WAAW,CAAC,IAAI,GAAG;gBACrB,OAAO,IAAI,CAAC,MAAM,OAAO,gBAAgB,eAAe,IAAI;oBAC1D,WAAW,CAAC,IAAI,GAAG,OAAO;gBAC5B,OAAO;oBACL,WAAW,CAAC,IAAI,GAAG;gBACrB;YACF;QACF;QAEA,OAAO;YAAE;YAAa;QAAQ;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,gCAAgC;QAC7C,OAAO;YAAE,aAAa,CAAC;YAAG,SAAS;QAAS;IAC9C;AACF;AAKO,SAAS,kBACd,QAAgB,EAChB,OAAgC;IAEhC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,mBAAmB;IACpD,MAAM,qBAAqB;QAAE,GAAG,WAAW;QAAE,GAAG,OAAO;IAAC;IAExD,IAAI,OAAO,IAAI,CAAC,oBAAoB,MAAM,KAAK,GAAG;QAChD,OAAO;IACT;IAEA,MAAM,iBAAiB,OAAO,OAAO,CAAC,oBACnC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;QAChB,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO,GAAG,IAAI,GAAG,EAAE,MAAM,CAAC,CAAC;QAC7B;QACA,OAAO,GAAG,IAAI,EAAE,EAAE,OAAO;IAC3B,GACC,IAAI,CAAC;IAER,OAAO,CAAC,KAAK,EAAE,eAAe,OAAO,EAAE,SAAS;AAClD;AAKO,SAAS,gBAAgB,QAAgB;IAM9C,MAAM,QAAQ,SAAS,KAAK,CAAC;IAC7B,MAAM,WAA6E,EAAE;IAErF,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,IAAI,OAAO;YACT,MAAM,QAAQ,KAAK,CAAC,EAAE,CAAC,MAAM;YAC7B,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;YAC1B,MAAM,KAAK,KACR,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,UAAU;YAErB,SAAS,IAAI,CAAC;gBACZ;gBACA;gBACA;gBACA,MAAM,QAAQ;YAChB;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,wBAAwB,QAAgB;IACtD,MAAM,WAAW,gBAAgB;IAEjC,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO;IACT;IAEA,MAAM,MAAM,SACT,GAAG,CAAC,CAAA;QACH,MAAM,SAAS,KAAK,MAAM,CAAC,QAAQ,KAAK,GAAG;QAC3C,OAAO,GAAG,OAAO,GAAG,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IACvD,GACC,IAAI,CAAC;IAER,OAAO,CAAC,wBAAwB,EAAE,IAAI,EAAE,CAAC;AAC3C;AAKO,SAAS,iBAAiB,QAAgB;IAY/C,MAAM,EAAE,OAAO,EAAE,GAAG,mBAAmB;IAEvC,uCAAuC;IACvC,MAAM,oBAAoB,QAAQ,OAAO,CAAC,mBAAmB;IAE7D,uCAAuC;IACvC,MAAM,oBAAoB,kBAAkB,OAAO,CAAC,YAAY;IAEhE,6DAA6D;IAC7D,MAAM,QAAQ,kBACX,IAAI,GACJ,KAAK,CAAC,OACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IAEzC,MAAM,aAAa,QAAQ,MAAM;IACjC,MAAM,qBAAqB,QAAQ,OAAO,CAAC,OAAO,IAAI,MAAM;IAE5D,uEAAuE;IACvE,MAAM,aAAa,QAChB,KAAK,CAAC,MACN,MAAM,CAAC,CAAA;QACN,MAAM,UAAU,KAAK,IAAI;QACzB,OAAO,QAAQ,MAAM,GAAG,KACjB,CAAC,QAAQ,UAAU,CAAC,QACpB,CAAC,QAAQ,UAAU,CAAC,QACpB,CAAC,QAAQ,UAAU,CAAC,QACpB,CAAC,QAAQ,UAAU,CAAC,QACpB,CAAC,QAAQ,KAAK,CAAC,aACf,CAAC,QAAQ,UAAU,CAAC;IAC7B,GAAG,MAAM;IAEX,MAAM,WAAW,CAAC,QAAQ,KAAK,CAAC,sBAAsB,EAAE,EAAE,MAAM;IAChE,MAAM,QAAQ,CAAC,QAAQ,KAAK,CAAC,+BAA+B,EAAE,EAAE,MAAM;IACtE,MAAM,SAAS,CAAC,QAAQ,KAAK,CAAC,gCAAgC,EAAE,EAAE,MAAM;IACxE,MAAM,aAAa,CAAC,QAAQ,KAAK,CAAC,sBAAsB,EAAE,EAAE,MAAM;IAClE,MAAM,QAAQ,CAAC,QAAQ,KAAK,CAAC,uBAAuB,EAAE,EAAE,MAAM,GAChD,CAAC,QAAQ,KAAK,CAAC,uBAAuB,EAAE,EAAE,MAAM;IAE9D,uDAAuD;IACvD,MAAM,cAAc,KAAK,IAAI,CAAC,QAAQ;IAEtC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,iBAAiB,QAAgB;IAM/C,MAAM,SAA8F,EAAE;IACtG,MAAM,QAAQ,SAAS,KAAK,CAAC;IAE7B,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,QAAQ;QAE3B,wCAAwC;QACxC,MAAM,cAAc,KAAK,KAAK,CAAC;QAC/B,IAAI,aAAa;YACf,YAAY,OAAO,CAAC,CAAA;gBAClB,MAAM,SAAS,KAAK,OAAO,CAAC,SAAS;gBACrC,IAAI,CAAC,KAAK,QAAQ,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG;oBAC/B,OAAO,IAAI,CAAC;wBACV,MAAM;wBACN;wBACA,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;QACF;QAEA,kCAAkC;QAClC,MAAM,mBAAmB,KAAK,KAAK,CAAC;QACpC,IAAI,oBAAoB,iBAAiB,MAAM,GAAG,MAAM,GAAG;YACzD,yEAAyE;YACzE,MAAM,SAAS,KAAK,OAAO,CAAC,SAAS;YACrC,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN;gBACA,SAAS;gBACT,MAAM;YACR;QACF;QAEA,2BAA2B;QAC3B,IAAI,KAAK,KAAK,CAAC,gBAAgB;YAC7B,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,MAAM;YACR;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,cAAc,QAAgB;IAC5C,OAAO,QACL,+BAA+B;KAC9B,OAAO,CAAC,WAAW,OACpB,6BAA6B;KAC5B,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,IACxB,IAAI,CAAC,KACN,6BAA6B;KAC5B,IAAI;AACT;AAKO,SAAS,oBAAoB,QAAgB;IAClD,MAAM,EAAE,OAAO,EAAE,GAAG,mBAAmB;IAEvC,OAAO,OACL,qBAAqB;KACpB,OAAO,CAAC,mBAAmB,GAC5B,qBAAqB;KACpB,OAAO,CAAC,YAAY,GACrB,gBAAgB;KACf,OAAO,CAAC,6BAA6B,KACtC,6BAA6B;KAC5B,OAAO,CAAC,4BAA4B,KACrC,0BAA0B;KACzB,OAAO,CAAC,gBAAgB,GACzB,qBAAqB;KACpB,OAAO,CAAC,oBAAoB,MAC5B,OAAO,CAAC,gBAAgB,KACzB,uBAAuB;KACtB,OAAO,CAAC,gBAAgB,KACzB,qBAAqB;KACpB,OAAO,CAAC,WAAW,GACpB,sBAAsB;KACrB,OAAO,CAAC,oBAAoB,IAC5B,OAAO,CAAC,oBAAoB,GAC7B,0BAA0B;KACzB,OAAO,CAAC,YAAY,GACrB,sBAAsB;KACrB,OAAO,CAAC,WAAW,QACnB,IAAI;AACT", "debugId": null}}, {"offset": {"line": 2890, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/utils/keyboard.ts"], "sourcesContent": ["/**\n * Keyboard shortcut utilities\n */\n\nexport interface KeyboardShortcut {\n  key: string\n  ctrl?: boolean\n  meta?: boolean\n  shift?: boolean\n  alt?: boolean\n}\n\n/**\n * Parse a keyboard shortcut string into components\n * Examples: \"ctrl+b\", \"cmd+shift+k\", \"F11\"\n */\nexport function parseShortcut(shortcut: string): KeyboardShortcut {\n  const parts = shortcut.toLowerCase().split('+')\n  const result: KeyboardShortcut = {\n    key: parts[parts.length - 1],\n  }\n  \n  parts.slice(0, -1).forEach(modifier => {\n    switch (modifier) {\n      case 'ctrl':\n        result.ctrl = true\n        break\n      case 'cmd':\n      case 'meta':\n        result.meta = true\n        break\n      case 'shift':\n        result.shift = true\n        break\n      case 'alt':\n        result.alt = true\n        break\n    }\n  })\n  \n  return result\n}\n\n/**\n * Check if a keyboard event matches a shortcut\n */\nexport function matchesShortcut(event: KeyboardEvent, shortcut: string): boolean {\n  const parsed = parseShortcut(shortcut)\n  \n  // Normalize key names\n  const eventKey = event.key.toLowerCase()\n  const shortcutKey = parsed.key.toLowerCase()\n  \n  // Check key match\n  const keyMatches = eventKey === shortcutKey || \n                    (shortcutKey === 'space' && eventKey === ' ') ||\n                    (shortcutKey === 'enter' && eventKey === 'enter') ||\n                    (shortcutKey === 'tab' && eventKey === 'tab') ||\n                    (shortcutKey === 'escape' && eventKey === 'escape')\n  \n  if (!keyMatches) return false\n  \n  // Check modifiers\n  const ctrlMatches = !!parsed.ctrl === event.ctrlKey\n  const metaMatches = !!parsed.meta === event.metaKey\n  const shiftMatches = !!parsed.shift === event.shiftKey\n  const altMatches = !!parsed.alt === event.altKey\n  \n  return ctrlMatches && metaMatches && shiftMatches && altMatches\n}\n\n/**\n * Get a human-readable description of a shortcut\n */\nexport function getShortcutDescription(shortcut: string): string {\n  const parsed = parseShortcut(shortcut)\n  const parts: string[] = []\n  \n  // Detect platform\n  const isMac = typeof navigator !== 'undefined' && \n               navigator.platform.toUpperCase().indexOf('MAC') >= 0\n  \n  if (parsed.ctrl) {\n    parts.push(isMac ? '⌃' : 'Ctrl')\n  }\n  \n  if (parsed.meta) {\n    parts.push(isMac ? '⌘' : 'Win')\n  }\n  \n  if (parsed.alt) {\n    parts.push(isMac ? '⌥' : 'Alt')\n  }\n  \n  if (parsed.shift) {\n    parts.push(isMac ? '⇧' : 'Shift')\n  }\n  \n  // Capitalize and format key\n  let key = parsed.key\n  if (key.length === 1) {\n    key = key.toUpperCase()\n  } else {\n    key = key.charAt(0).toUpperCase() + key.slice(1)\n  }\n  \n  parts.push(key)\n  \n  return parts.join(isMac ? '' : '+')\n}\n\n/**\n * Create a keyboard shortcut handler\n */\nexport function createShortcutHandler(\n  shortcuts: Record<string, () => void>\n): (event: KeyboardEvent) => void {\n  return (event: KeyboardEvent) => {\n    for (const [shortcut, handler] of Object.entries(shortcuts)) {\n      if (matchesShortcut(event, shortcut)) {\n        event.preventDefault()\n        handler()\n        break\n      }\n    }\n  }\n}\n\n/**\n * Common keyboard shortcuts for markdown editing\n */\nexport const COMMON_SHORTCUTS = {\n  BOLD: ['ctrl+b', 'cmd+b'],\n  ITALIC: ['ctrl+i', 'cmd+i'],\n  UNDERLINE: ['ctrl+u', 'cmd+u'],\n  STRIKETHROUGH: ['ctrl+shift+x', 'cmd+shift+x'],\n  CODE: ['ctrl+`', 'cmd+`'],\n  CODE_BLOCK: ['ctrl+shift+c', 'cmd+shift+c'],\n  LINK: ['ctrl+k', 'cmd+k'],\n  IMAGE: ['ctrl+shift+i', 'cmd+shift+i'],\n  HEADING_1: ['ctrl+1', 'cmd+1'],\n  HEADING_2: ['ctrl+2', 'cmd+2'],\n  HEADING_3: ['ctrl+3', 'cmd+3'],\n  UNORDERED_LIST: ['ctrl+shift+8', 'cmd+shift+8'],\n  ORDERED_LIST: ['ctrl+shift+7', 'cmd+shift+7'],\n  QUOTE: ['ctrl+shift+.', 'cmd+shift+.'],\n  HORIZONTAL_RULE: ['ctrl+shift+-', 'cmd+shift+-'],\n  SAVE: ['ctrl+s', 'cmd+s'],\n  UNDO: ['ctrl+z', 'cmd+z'],\n  REDO: ['ctrl+y', 'cmd+y', 'ctrl+shift+z', 'cmd+shift+z'],\n  FIND: ['ctrl+f', 'cmd+f'],\n  REPLACE: ['ctrl+h', 'cmd+h'],\n  FULLSCREEN: ['F11'],\n  PREVIEW: ['ctrl+shift+p', 'cmd+shift+p'],\n  LIVE_PREVIEW: ['ctrl+shift+l', 'cmd+shift+l'],\n} as const\n\n/**\n * Get the primary shortcut for the current platform\n */\nexport function getPrimaryShortcut(shortcuts: readonly string[]): string {\n  const isMac = typeof navigator !== 'undefined' && \n               navigator.platform.toUpperCase().indexOf('MAC') >= 0\n  \n  // Find the first shortcut that matches the platform\n  for (const shortcut of shortcuts) {\n    const hasCmd = shortcut.includes('cmd')\n    const hasCtrl = shortcut.includes('ctrl')\n    \n    if (isMac && hasCmd) return shortcut\n    if (!isMac && hasCtrl) return shortcut\n  }\n  \n  // Fallback to first shortcut\n  return shortcuts[0]\n}\n\n/**\n * Check if an element should receive keyboard shortcuts\n */\nexport function shouldReceiveShortcuts(element: Element): boolean {\n  const tagName = element.tagName.toLowerCase()\n  \n  // Don't handle shortcuts in form inputs (except our editor)\n  if (['input', 'select', 'option'].includes(tagName)) {\n    return false\n  }\n  \n  // Don't handle shortcuts in contenteditable elements (except our editor)\n  if (element.getAttribute('contenteditable') === 'true') {\n    return false\n  }\n  \n  // Allow shortcuts in our markdown editor textarea\n  if (element.closest('.markdown-editor')) {\n    return true\n  }\n  \n  // Don't handle shortcuts in other textareas\n  if (tagName === 'textarea') {\n    return false\n  }\n  \n  return true\n}\n\n/**\n * Add global keyboard shortcut listeners\n */\nexport function addGlobalShortcuts(\n  shortcuts: Record<string, () => void>,\n  options: {\n    preventDefault?: boolean\n    checkTarget?: boolean\n  } = {}\n): () => void {\n  const { preventDefault = true, checkTarget = true } = options\n  \n  const handler = (event: KeyboardEvent) => {\n    // Check if the target should receive shortcuts\n    if (checkTarget && event.target && !shouldReceiveShortcuts(event.target as Element)) {\n      return\n    }\n    \n    for (const [shortcut, callback] of Object.entries(shortcuts)) {\n      if (matchesShortcut(event, shortcut)) {\n        if (preventDefault) {\n          event.preventDefault()\n        }\n        callback()\n        break\n      }\n    }\n  }\n  \n  document.addEventListener('keydown', handler)\n  \n  // Return cleanup function\n  return () => {\n    document.removeEventListener('keydown', handler)\n  }\n}\n\n/**\n * Format shortcut for display in tooltips\n */\nexport function formatShortcutForTooltip(shortcuts: readonly string[]): string {\n  const primary = getPrimaryShortcut(shortcuts)\n  return getShortcutDescription(primary)\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;AAcM,SAAS,cAAc,QAAgB;IAC5C,MAAM,QAAQ,SAAS,WAAW,GAAG,KAAK,CAAC;IAC3C,MAAM,SAA2B;QAC/B,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;IAC9B;IAEA,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAA;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,GAAG;gBACd;YACF,KAAK;YACL,KAAK;gBACH,OAAO,IAAI,GAAG;gBACd;YACF,KAAK;gBACH,OAAO,KAAK,GAAG;gBACf;YACF,KAAK;gBACH,OAAO,GAAG,GAAG;gBACb;QACJ;IACF;IAEA,OAAO;AACT;AAKO,SAAS,gBAAgB,KAAoB,EAAE,QAAgB;IACpE,MAAM,SAAS,cAAc;IAE7B,sBAAsB;IACtB,MAAM,WAAW,MAAM,GAAG,CAAC,WAAW;IACtC,MAAM,cAAc,OAAO,GAAG,CAAC,WAAW;IAE1C,kBAAkB;IAClB,MAAM,aAAa,aAAa,eACb,gBAAgB,WAAW,aAAa,OACxC,gBAAgB,WAAW,aAAa,WACxC,gBAAgB,SAAS,aAAa,SACtC,gBAAgB,YAAY,aAAa;IAE5D,IAAI,CAAC,YAAY,OAAO;IAExB,kBAAkB;IAClB,MAAM,cAAc,CAAC,CAAC,OAAO,IAAI,KAAK,MAAM,OAAO;IACnD,MAAM,cAAc,CAAC,CAAC,OAAO,IAAI,KAAK,MAAM,OAAO;IACnD,MAAM,eAAe,CAAC,CAAC,OAAO,KAAK,KAAK,MAAM,QAAQ;IACtD,MAAM,aAAa,CAAC,CAAC,OAAO,GAAG,KAAK,MAAM,MAAM;IAEhD,OAAO,eAAe,eAAe,gBAAgB;AACvD;AAKO,SAAS,uBAAuB,QAAgB;IACrD,MAAM,SAAS,cAAc;IAC7B,MAAM,QAAkB,EAAE;IAE1B,kBAAkB;IAClB,MAAM,QAAQ,OAAO,cAAc,eACtB,UAAU,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU;IAEhE,IAAI,OAAO,IAAI,EAAE;QACf,MAAM,IAAI,CAAC,QAAQ,MAAM;IAC3B;IAEA,IAAI,OAAO,IAAI,EAAE;QACf,MAAM,IAAI,CAAC,QAAQ,MAAM;IAC3B;IAEA,IAAI,OAAO,GAAG,EAAE;QACd,MAAM,IAAI,CAAC,QAAQ,MAAM;IAC3B;IAEA,IAAI,OAAO,KAAK,EAAE;QAChB,MAAM,IAAI,CAAC,QAAQ,MAAM;IAC3B;IAEA,4BAA4B;IAC5B,IAAI,MAAM,OAAO,GAAG;IACpB,IAAI,IAAI,MAAM,KAAK,GAAG;QACpB,MAAM,IAAI,WAAW;IACvB,OAAO;QACL,MAAM,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;IAChD;IAEA,MAAM,IAAI,CAAC;IAEX,OAAO,MAAM,IAAI,CAAC,QAAQ,KAAK;AACjC;AAKO,SAAS,sBACd,SAAqC;IAErC,OAAO,CAAC;QACN,KAAK,MAAM,CAAC,UAAU,QAAQ,IAAI,OAAO,OAAO,CAAC,WAAY;YAC3D,IAAI,gBAAgB,OAAO,WAAW;gBACpC,MAAM,cAAc;gBACpB;gBACA;YACF;QACF;IACF;AACF;AAKO,MAAM,mBAAmB;IAC9B,MAAM;QAAC;QAAU;KAAQ;IACzB,QAAQ;QAAC;QAAU;KAAQ;IAC3B,WAAW;QAAC;QAAU;KAAQ;IAC9B,eAAe;QAAC;QAAgB;KAAc;IAC9C,MAAM;QAAC;QAAU;KAAQ;IACzB,YAAY;QAAC;QAAgB;KAAc;IAC3C,MAAM;QAAC;QAAU;KAAQ;IACzB,OAAO;QAAC;QAAgB;KAAc;IACtC,WAAW;QAAC;QAAU;KAAQ;IAC9B,WAAW;QAAC;QAAU;KAAQ;IAC9B,WAAW;QAAC;QAAU;KAAQ;IAC9B,gBAAgB;QAAC;QAAgB;KAAc;IAC/C,cAAc;QAAC;QAAgB;KAAc;IAC7C,OAAO;QAAC;QAAgB;KAAc;IACtC,iBAAiB;QAAC;QAAgB;KAAc;IAChD,MAAM;QAAC;QAAU;KAAQ;IACzB,MAAM;QAAC;QAAU;KAAQ;IACzB,MAAM;QAAC;QAAU;QAAS;QAAgB;KAAc;IACxD,MAAM;QAAC;QAAU;KAAQ;IACzB,SAAS;QAAC;QAAU;KAAQ;IAC5B,YAAY;QAAC;KAAM;IACnB,SAAS;QAAC;QAAgB;KAAc;IACxC,cAAc;QAAC;QAAgB;KAAc;AAC/C;AAKO,SAAS,mBAAmB,SAA4B;IAC7D,MAAM,QAAQ,OAAO,cAAc,eACtB,UAAU,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU;IAEhE,oDAAoD;IACpD,KAAK,MAAM,YAAY,UAAW;QAChC,MAAM,SAAS,SAAS,QAAQ,CAAC;QACjC,MAAM,UAAU,SAAS,QAAQ,CAAC;QAElC,IAAI,SAAS,QAAQ,OAAO;QAC5B,IAAI,CAAC,SAAS,SAAS,OAAO;IAChC;IAEA,6BAA6B;IAC7B,OAAO,SAAS,CAAC,EAAE;AACrB;AAKO,SAAS,uBAAuB,OAAgB;IACrD,MAAM,UAAU,QAAQ,OAAO,CAAC,WAAW;IAE3C,4DAA4D;IAC5D,IAAI;QAAC;QAAS;QAAU;KAAS,CAAC,QAAQ,CAAC,UAAU;QACnD,OAAO;IACT;IAEA,yEAAyE;IACzE,IAAI,QAAQ,YAAY,CAAC,uBAAuB,QAAQ;QACtD,OAAO;IACT;IAEA,kDAAkD;IAClD,IAAI,QAAQ,OAAO,CAAC,qBAAqB;QACvC,OAAO;IACT;IAEA,4CAA4C;IAC5C,IAAI,YAAY,YAAY;QAC1B,OAAO;IACT;IAEA,OAAO;AACT;AAKO,SAAS,mBACd,SAAqC,EACrC,UAGI,CAAC,CAAC;IAEN,MAAM,EAAE,iBAAiB,IAAI,EAAE,cAAc,IAAI,EAAE,GAAG;IAEtD,MAAM,UAAU,CAAC;QACf,+CAA+C;QAC/C,IAAI,eAAe,MAAM,MAAM,IAAI,CAAC,uBAAuB,MAAM,MAAM,GAAc;YACnF;QACF;QAEA,KAAK,MAAM,CAAC,UAAU,SAAS,IAAI,OAAO,OAAO,CAAC,WAAY;YAC5D,IAAI,gBAAgB,OAAO,WAAW;gBACpC,IAAI,gBAAgB;oBAClB,MAAM,cAAc;gBACtB;gBACA;gBACA;YACF;QACF;IACF;IAEA,SAAS,gBAAgB,CAAC,WAAW;IAErC,0BAA0B;IAC1B,OAAO;QACL,SAAS,mBAAmB,CAAC,WAAW;IAC1C;AACF;AAKO,SAAS,yBAAyB,SAA4B;IACnE,MAAM,UAAU,mBAAmB;IACnC,OAAO,uBAAuB;AAChC", "debugId": null}}, {"offset": {"line": 3144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/utils/validation.ts"], "sourcesContent": ["/**\n * Validation utilities for markdown editor\n */\n\nexport interface ValidationError {\n  line: number\n  column: number\n  message: string\n  type: 'error' | 'warning' | 'info'\n  code: string\n}\n\nexport interface ValidationOptions {\n  maxLength?: number\n  maxLines?: number\n  allowHtml?: boolean\n  requireFrontmatter?: boolean\n  allowedTags?: string[]\n  blockedWords?: string[]\n  checkLinks?: boolean\n  checkImages?: boolean\n}\n\n/**\n * Validate markdown content\n */\nexport function validateMarkdown(\n  content: string, \n  options: ValidationOptions = {}\n): ValidationError[] {\n  const errors: ValidationError[] = []\n  const lines = content.split('\\n')\n  \n  const {\n    maxLength = 50000,\n    maxLines = 1000,\n    allowHtml = false,\n    requireFrontmatter = false,\n    allowedTags = [],\n    blockedWords = [],\n    checkLinks = true,\n    checkImages = true,\n  } = options\n  \n  // Check content length\n  if (content.length > maxLength) {\n    errors.push({\n      line: 1,\n      column: 1,\n      message: `Content exceeds maximum length of ${maxLength} characters`,\n      type: 'error',\n      code: 'MAX_LENGTH_EXCEEDED',\n    })\n  }\n  \n  // Check line count\n  if (lines.length > maxLines) {\n    errors.push({\n      line: maxLines + 1,\n      column: 1,\n      message: `Content exceeds maximum of ${maxLines} lines`,\n      type: 'error',\n      code: 'MAX_LINES_EXCEEDED',\n    })\n  }\n  \n  // Check for frontmatter if required\n  if (requireFrontmatter && !content.startsWith('---')) {\n    errors.push({\n      line: 1,\n      column: 1,\n      message: 'Frontmatter is required',\n      type: 'error',\n      code: 'MISSING_FRONTMATTER',\n    })\n  }\n  \n  // Validate each line\n  lines.forEach((line, index) => {\n    const lineNumber = index + 1\n    \n    // Check for HTML tags if not allowed\n    if (!allowHtml) {\n      const htmlMatches = line.match(/<[^>]+>/g)\n      if (htmlMatches) {\n        htmlMatches.forEach(tag => {\n          const column = line.indexOf(tag) + 1\n          const tagName = tag.match(/<\\/?([a-zA-Z][a-zA-Z0-9]*)/)?.[1]\n          \n          if (tagName && !allowedTags.includes(tagName.toLowerCase())) {\n            errors.push({\n              line: lineNumber,\n              column,\n              message: `HTML tag '${tagName}' is not allowed`,\n              type: 'error',\n              code: 'HTML_NOT_ALLOWED',\n            })\n          }\n        })\n      }\n    }\n    \n    // Check for blocked words\n    if (blockedWords.length > 0) {\n      blockedWords.forEach(word => {\n        const regex = new RegExp(`\\\\b${word}\\\\b`, 'gi')\n        let match\n        while ((match = regex.exec(line)) !== null) {\n          errors.push({\n            line: lineNumber,\n            column: match.index + 1,\n            message: `Blocked word '${word}' found`,\n            type: 'warning',\n            code: 'BLOCKED_WORD',\n          })\n        }\n      })\n    }\n    \n    // Check links\n    if (checkLinks) {\n      const linkMatches = line.matchAll(/\\[([^\\]]*)\\]\\(([^)]+)\\)/g)\n      for (const match of linkMatches) {\n        const [, text, url] = match\n        const column = match.index! + 1\n        \n        // Check for empty link text\n        if (!text.trim()) {\n          errors.push({\n            line: lineNumber,\n            column,\n            message: 'Link has empty text',\n            type: 'warning',\n            code: 'EMPTY_LINK_TEXT',\n          })\n        }\n        \n        // Check for empty URL\n        if (!url.trim()) {\n          errors.push({\n            line: lineNumber,\n            column: column + text.length + 3,\n            message: 'Link has empty URL',\n            type: 'error',\n            code: 'EMPTY_LINK_URL',\n          })\n        }\n        \n        // Check for malformed URLs\n        if (url.trim() && !isValidUrl(url)) {\n          errors.push({\n            line: lineNumber,\n            column: column + text.length + 3,\n            message: 'Link URL appears to be malformed',\n            type: 'warning',\n            code: 'MALFORMED_URL',\n          })\n        }\n      }\n    }\n    \n    // Check images\n    if (checkImages) {\n      const imageMatches = line.matchAll(/!\\[([^\\]]*)\\]\\(([^)]+)\\)/g)\n      for (const match of imageMatches) {\n        const [, alt, src] = match\n        const column = match.index! + 1\n        \n        // Check for empty alt text\n        if (!alt.trim()) {\n          errors.push({\n            line: lineNumber,\n            column,\n            message: 'Image has empty alt text',\n            type: 'warning',\n            code: 'EMPTY_IMAGE_ALT',\n          })\n        }\n        \n        // Check for empty src\n        if (!src.trim()) {\n          errors.push({\n            line: lineNumber,\n            column: column + alt.length + 4,\n            message: 'Image has empty source',\n            type: 'error',\n            code: 'EMPTY_IMAGE_SRC',\n          })\n        }\n      }\n    }\n    \n    // Check for unmatched code blocks\n    const codeBlockMatches = line.match(/```/g)\n    if (codeBlockMatches && codeBlockMatches.length % 2 !== 0) {\n      const column = line.indexOf('```') + 1\n      errors.push({\n        line: lineNumber,\n        column,\n        message: 'Potentially unmatched code block',\n        type: 'warning',\n        code: 'UNMATCHED_CODE_BLOCK',\n      })\n    }\n    \n    // Check for empty headings\n    if (line.match(/^#{1,6}\\s*$/)) {\n      errors.push({\n        line: lineNumber,\n        column: 1,\n        message: 'Empty heading',\n        type: 'warning',\n        code: 'EMPTY_HEADING',\n      })\n    }\n    \n    // Check for excessive heading levels\n    const headingMatch = line.match(/^(#{7,})\\s/)\n    if (headingMatch) {\n      errors.push({\n        line: lineNumber,\n        column: 1,\n        message: 'Heading level too deep (maximum is 6)',\n        type: 'error',\n        code: 'HEADING_TOO_DEEP',\n      })\n    }\n    \n    // Check for trailing whitespace\n    if (line.endsWith(' ') || line.endsWith('\\t')) {\n      errors.push({\n        line: lineNumber,\n        column: line.length,\n        message: 'Line has trailing whitespace',\n        type: 'info',\n        code: 'TRAILING_WHITESPACE',\n      })\n    }\n  })\n  \n  return errors\n}\n\n/**\n * Check if a URL is valid\n */\nfunction isValidUrl(url: string): boolean {\n  // Allow relative URLs\n  if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {\n    return true\n  }\n  \n  // Allow anchor links\n  if (url.startsWith('#')) {\n    return true\n  }\n  \n  // Check absolute URLs\n  try {\n    new URL(url)\n    return true\n  } catch {\n    return false\n  }\n}\n\n/**\n * Validate frontmatter\n */\nexport function validateFrontmatter(\n  frontmatter: string,\n  requiredFields: string[] = [],\n  allowedFields: string[] = []\n): ValidationError[] {\n  const errors: ValidationError[] = []\n  const lines = frontmatter.split('\\n')\n  const fields = new Set<string>()\n  \n  lines.forEach((line, index) => {\n    const lineNumber = index + 1\n    const colonIndex = line.indexOf(':')\n    \n    if (line.trim() && colonIndex > 0) {\n      const key = line.substring(0, colonIndex).trim()\n      const value = line.substring(colonIndex + 1).trim()\n      \n      fields.add(key)\n      \n      // Check if field is allowed\n      if (allowedFields.length > 0 && !allowedFields.includes(key)) {\n        errors.push({\n          line: lineNumber,\n          column: 1,\n          message: `Field '${key}' is not allowed`,\n          type: 'warning',\n          code: 'FIELD_NOT_ALLOWED',\n        })\n      }\n      \n      // Check for empty values\n      if (!value) {\n        errors.push({\n          line: lineNumber,\n          column: colonIndex + 2,\n          message: `Field '${key}' has empty value`,\n          type: 'warning',\n          code: 'EMPTY_FIELD_VALUE',\n        })\n      }\n    } else if (line.trim() && !line.includes(':')) {\n      errors.push({\n        line: lineNumber,\n        column: 1,\n        message: 'Invalid frontmatter syntax',\n        type: 'error',\n        code: 'INVALID_FRONTMATTER_SYNTAX',\n      })\n    }\n  })\n  \n  // Check for required fields\n  requiredFields.forEach(field => {\n    if (!fields.has(field)) {\n      errors.push({\n        line: 1,\n        column: 1,\n        message: `Required field '${field}' is missing`,\n        type: 'error',\n        code: 'MISSING_REQUIRED_FIELD',\n      })\n    }\n  })\n  \n  return errors\n}\n\n/**\n * Get validation summary\n */\nexport function getValidationSummary(errors: ValidationError[]): {\n  errorCount: number\n  warningCount: number\n  infoCount: number\n  hasErrors: boolean\n  hasWarnings: boolean\n} {\n  const errorCount = errors.filter(e => e.type === 'error').length\n  const warningCount = errors.filter(e => e.type === 'warning').length\n  const infoCount = errors.filter(e => e.type === 'info').length\n  \n  return {\n    errorCount,\n    warningCount,\n    infoCount,\n    hasErrors: errorCount > 0,\n    hasWarnings: warningCount > 0,\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAwBM,SAAS,iBACd,OAAe,EACf,UAA6B,CAAC,CAAC;IAE/B,MAAM,SAA4B,EAAE;IACpC,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAE5B,MAAM,EACJ,YAAY,KAAK,EACjB,WAAW,IAAI,EACf,YAAY,KAAK,EACjB,qBAAqB,KAAK,EAC1B,cAAc,EAAE,EAChB,eAAe,EAAE,EACjB,aAAa,IAAI,EACjB,cAAc,IAAI,EACnB,GAAG;IAEJ,uBAAuB;IACvB,IAAI,QAAQ,MAAM,GAAG,WAAW;QAC9B,OAAO,IAAI,CAAC;YACV,MAAM;YACN,QAAQ;YACR,SAAS,CAAC,kCAAkC,EAAE,UAAU,WAAW,CAAC;YACpE,MAAM;YACN,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,IAAI,MAAM,MAAM,GAAG,UAAU;QAC3B,OAAO,IAAI,CAAC;YACV,MAAM,WAAW;YACjB,QAAQ;YACR,SAAS,CAAC,2BAA2B,EAAE,SAAS,MAAM,CAAC;YACvD,MAAM;YACN,MAAM;QACR;IACF;IAEA,oCAAoC;IACpC,IAAI,sBAAsB,CAAC,QAAQ,UAAU,CAAC,QAAQ;QACpD,OAAO,IAAI,CAAC;YACV,MAAM;YACN,QAAQ;YACR,SAAS;YACT,MAAM;YACN,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,QAAQ;QAE3B,qCAAqC;QACrC,IAAI,CAAC,WAAW;YACd,MAAM,cAAc,KAAK,KAAK,CAAC;YAC/B,IAAI,aAAa;gBACf,YAAY,OAAO,CAAC,CAAA;oBAClB,MAAM,SAAS,KAAK,OAAO,CAAC,OAAO;oBACnC,MAAM,UAAU,IAAI,KAAK,CAAC,+BAA+B,CAAC,EAAE;oBAE5D,IAAI,WAAW,CAAC,YAAY,QAAQ,CAAC,QAAQ,WAAW,KAAK;wBAC3D,OAAO,IAAI,CAAC;4BACV,MAAM;4BACN;4BACA,SAAS,CAAC,UAAU,EAAE,QAAQ,gBAAgB,CAAC;4BAC/C,MAAM;4BACN,MAAM;wBACR;oBACF;gBACF;YACF;QACF;QAEA,0BAA0B;QAC1B,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,aAAa,OAAO,CAAC,CAAA;gBACnB,MAAM,QAAQ,IAAI,OAAO,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE;gBAC1C,IAAI;gBACJ,MAAO,CAAC,QAAQ,MAAM,IAAI,CAAC,KAAK,MAAM,KAAM;oBAC1C,OAAO,IAAI,CAAC;wBACV,MAAM;wBACN,QAAQ,MAAM,KAAK,GAAG;wBACtB,SAAS,CAAC,cAAc,EAAE,KAAK,OAAO,CAAC;wBACvC,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;QACF;QAEA,cAAc;QACd,IAAI,YAAY;YACd,MAAM,cAAc,KAAK,QAAQ,CAAC;YAClC,KAAK,MAAM,SAAS,YAAa;gBAC/B,MAAM,GAAG,MAAM,IAAI,GAAG;gBACtB,MAAM,SAAS,MAAM,KAAK,GAAI;gBAE9B,4BAA4B;gBAC5B,IAAI,CAAC,KAAK,IAAI,IAAI;oBAChB,OAAO,IAAI,CAAC;wBACV,MAAM;wBACN;wBACA,SAAS;wBACT,MAAM;wBACN,MAAM;oBACR;gBACF;gBAEA,sBAAsB;gBACtB,IAAI,CAAC,IAAI,IAAI,IAAI;oBACf,OAAO,IAAI,CAAC;wBACV,MAAM;wBACN,QAAQ,SAAS,KAAK,MAAM,GAAG;wBAC/B,SAAS;wBACT,MAAM;wBACN,MAAM;oBACR;gBACF;gBAEA,2BAA2B;gBAC3B,IAAI,IAAI,IAAI,MAAM,CAAC,WAAW,MAAM;oBAClC,OAAO,IAAI,CAAC;wBACV,MAAM;wBACN,QAAQ,SAAS,KAAK,MAAM,GAAG;wBAC/B,SAAS;wBACT,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;QACF;QAEA,eAAe;QACf,IAAI,aAAa;YACf,MAAM,eAAe,KAAK,QAAQ,CAAC;YACnC,KAAK,MAAM,SAAS,aAAc;gBAChC,MAAM,GAAG,KAAK,IAAI,GAAG;gBACrB,MAAM,SAAS,MAAM,KAAK,GAAI;gBAE9B,2BAA2B;gBAC3B,IAAI,CAAC,IAAI,IAAI,IAAI;oBACf,OAAO,IAAI,CAAC;wBACV,MAAM;wBACN;wBACA,SAAS;wBACT,MAAM;wBACN,MAAM;oBACR;gBACF;gBAEA,sBAAsB;gBACtB,IAAI,CAAC,IAAI,IAAI,IAAI;oBACf,OAAO,IAAI,CAAC;wBACV,MAAM;wBACN,QAAQ,SAAS,IAAI,MAAM,GAAG;wBAC9B,SAAS;wBACT,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;QACF;QAEA,kCAAkC;QAClC,MAAM,mBAAmB,KAAK,KAAK,CAAC;QACpC,IAAI,oBAAoB,iBAAiB,MAAM,GAAG,MAAM,GAAG;YACzD,MAAM,SAAS,KAAK,OAAO,CAAC,SAAS;YACrC,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN;gBACA,SAAS;gBACT,MAAM;gBACN,MAAM;YACR;QACF;QAEA,2BAA2B;QAC3B,IAAI,KAAK,KAAK,CAAC,gBAAgB;YAC7B,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,MAAM;gBACN,MAAM;YACR;QACF;QAEA,qCAAqC;QACrC,MAAM,eAAe,KAAK,KAAK,CAAC;QAChC,IAAI,cAAc;YAChB,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,MAAM;gBACN,MAAM;YACR;QACF;QAEA,gCAAgC;QAChC,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,OAAO;YAC7C,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,QAAQ,KAAK,MAAM;gBACnB,SAAS;gBACT,MAAM;gBACN,MAAM;YACR;QACF;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,WAAW,GAAW;IAC7B,sBAAsB;IACtB,IAAI,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,QAAQ;QACxE,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,IAAI,UAAU,CAAC,MAAM;QACvB,OAAO;IACT;IAEA,sBAAsB;IACtB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAKO,SAAS,oBACd,WAAmB,EACnB,iBAA2B,EAAE,EAC7B,gBAA0B,EAAE;IAE5B,MAAM,SAA4B,EAAE;IACpC,MAAM,QAAQ,YAAY,KAAK,CAAC;IAChC,MAAM,SAAS,IAAI;IAEnB,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,QAAQ;QAC3B,MAAM,aAAa,KAAK,OAAO,CAAC;QAEhC,IAAI,KAAK,IAAI,MAAM,aAAa,GAAG;YACjC,MAAM,MAAM,KAAK,SAAS,CAAC,GAAG,YAAY,IAAI;YAC9C,MAAM,QAAQ,KAAK,SAAS,CAAC,aAAa,GAAG,IAAI;YAEjD,OAAO,GAAG,CAAC;YAEX,4BAA4B;YAC5B,IAAI,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,MAAM;gBAC5D,OAAO,IAAI,CAAC;oBACV,MAAM;oBACN,QAAQ;oBACR,SAAS,CAAC,OAAO,EAAE,IAAI,gBAAgB,CAAC;oBACxC,MAAM;oBACN,MAAM;gBACR;YACF;YAEA,yBAAyB;YACzB,IAAI,CAAC,OAAO;gBACV,OAAO,IAAI,CAAC;oBACV,MAAM;oBACN,QAAQ,aAAa;oBACrB,SAAS,CAAC,OAAO,EAAE,IAAI,iBAAiB,CAAC;oBACzC,MAAM;oBACN,MAAM;gBACR;YACF;QACF,OAAO,IAAI,KAAK,IAAI,MAAM,CAAC,KAAK,QAAQ,CAAC,MAAM;YAC7C,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,MAAM;gBACN,MAAM;YACR;QACF;IACF;IAEA,4BAA4B;IAC5B,eAAe,OAAO,CAAC,CAAA;QACrB,IAAI,CAAC,OAAO,GAAG,CAAC,QAAQ;YACtB,OAAO,IAAI,CAAC;gBACV,MAAM;gBACN,QAAQ;gBACR,SAAS,CAAC,gBAAgB,EAAE,MAAM,YAAY,CAAC;gBAC/C,MAAM;gBACN,MAAM;YACR;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,qBAAqB,MAAyB;IAO5D,MAAM,aAAa,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;IAChE,MAAM,eAAe,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;IACpE,MAAM,YAAY,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,MAAM;IAE9D,OAAO;QACL;QACA;QACA;QACA,WAAW,aAAa;QACxB,aAAa,eAAe;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 3427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-editor/index.ts"], "sourcesContent": ["// Main editor component\nexport { MarkdownEditor } from './markdown-editor'\nexport type { MarkdownEditorRef } from './markdown-editor'\n\n// Context and hooks\nexport { EditorProvider, useEditor, useEditorState, useEditorDispatch } from './context'\nexport { useEditorTheme, useThemeVariables } from './hooks/use-theme'\nexport type { EditorTheme } from './hooks/use-theme'\n\n// Components\nexport { Textarea } from './components/textarea'\nexport type { TextareaRef } from './components/textarea'\nexport { Toolbar } from './components/toolbar'\nexport { MarkdownPreview } from './components/preview'\nexport type { MarkdownPreviewRef } from './components/preview'\nexport { DragBar } from './components/drag-bar'\n\n// Commands\nexport * from './commands/index'\n\n// Types\nexport type {\n  MarkdownEditorProps,\n  EditorState,\n  EditorAction,\n  Command,\n  TextState,\n  TextRange,\n  TextAreaAPI,\n  ToolbarProps,\n  PreviewProps,\n  DragBarProps,\n  TextareaProps,\n  CommandContext,\n  CommandsFilter,\n  EditorComponents,\n} from './types'\n\n// Utilities\nexport {\n  CommandOrchestrator,\n  TextAreaAPIImpl,\n  wrapText,\n  insertBlock,\n  toggleLinePrefix,\n  insertTextAtPosition,\n  getStateFromTextArea,\n  getWordCount,\n} from './utils/text-api'\n\nexport {\n  extractFrontmatter,\n  updateFrontmatter,\n  extractHeadings,\n  generateTableOfContents,\n  getMarkdownStats,\n  validateMarkdown as validateMarkdownSyntax,\n  cleanMarkdown,\n  markdownToPlainText,\n} from './utils/markdown'\n\nexport {\n  parseShortcut,\n  matchesShortcut,\n  getShortcutDescription,\n  createShortcutHandler,\n  getPrimaryShortcut,\n  shouldReceiveShortcuts,\n  addGlobalShortcuts,\n  formatShortcutForTooltip,\n  COMMON_SHORTCUTS,\n} from './utils/keyboard'\nexport type { KeyboardShortcut } from './utils/keyboard'\n\nexport {\n  validateMarkdown,\n  validateFrontmatter,\n  getValidationSummary,\n} from './utils/validation'\nexport type { ValidationError, ValidationOptions } from './utils/validation'\n\n// Default exports for convenience\nexport { getDefaultCommands, getDefaultExtraCommands } from './commands'\n"], "names": [], "mappings": "AAAA,wBAAwB;;AACxB;AAGA,oBAAoB;AACpB;AACA;AAGA,aAAa;AACb;AAEA;AACA;AAEA;AAEA,WAAW;AACX;AAoBA,YAAY;AACZ;AAWA;AAWA;AAaA", "debugId": null}}, {"offset": {"line": 3484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/test-fullscreen/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MarkdownEditor } from '@/components/markdown-editor'\n\nconst testContent = `# Fullscreen Test\n\nThis is a test page to verify that the fullscreen functionality works correctly.\n\n## Test Instructions\n\n1. Click the fullscreen button in the toolbar (⛶ icon)\n2. Verify that:\n   - The editor takes up the full screen\n   - All text content is visible and readable\n   - You can type and edit text normally\n   - Syntax highlighting works (if enabled)\n   - The cursor is visible\n   - Scrolling works properly\n\n## Sample Content\n\nHere's some sample markdown content to test with:\n\n### Code Block\n\n\\`\\`\\`javascript\nfunction testFullscreen() {\n  console.log('Testing fullscreen mode');\n  return 'All text should be visible!';\n}\n\\`\\`\\`\n\n### List\n\n- Item 1\n- Item 2\n- Item 3\n\n### Table\n\n| Column 1 | Column 2 | Column 3 |\n|----------|----------|----------|\n| Data 1   | Data 2   | Data 3   |\n| Data 4   | Data 5   | Data 6   |\n\n### Long Text\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n\nSed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.\n\n### More Content\n\nAdd more content here to test scrolling in fullscreen mode...\n\n**Bold text should be visible**\n*Italic text should be visible*\n\\`Inline code should be visible\\`\n\n> Blockquotes should be visible and properly formatted\n\n1. Numbered lists\n2. Should also work\n3. In fullscreen mode\n\n---\n\nEnd of test content. If you can see this text clearly in fullscreen mode, the fix is working!\n`\n\nexport default function TestFullscreenPage() {\n  const [content, setContent] = useState(testContent)\n\n  return (\n    <div className=\"min-h-screen bg-background p-4\">\n      <div className=\"max-w-6xl mx-auto\">\n        <h1 className=\"text-3xl font-bold mb-4\">Fullscreen Test Page</h1>\n        \n        <div className=\"mb-4 p-4 bg-card border border-border rounded-lg\">\n          <h2 className=\"text-xl font-semibold mb-2\">Test Instructions</h2>\n          <ol className=\"list-decimal list-inside space-y-1 text-sm\">\n            <li>Click the fullscreen button (⛶) in the editor toolbar</li>\n            <li>Verify that all text content is visible and readable</li>\n            <li>Test typing and editing functionality</li>\n            <li>Check that scrolling works properly</li>\n            <li>Press F11 or click the fullscreen button again to exit</li>\n          </ol>\n        </div>\n\n        <div className=\"border border-border rounded-lg overflow-hidden\">\n          <MarkdownEditor\n            value={content}\n            onChange={(value) => setContent(value || '')}\n            height=\"600px\"\n            preview=\"live\"\n            highlightEnable={true}\n          />\n        </div>\n\n        <div className=\"mt-4 p-4 bg-muted rounded-lg\">\n          <h3 className=\"font-semibold mb-2\">Expected Behavior:</h3>\n          <ul className=\"text-sm space-y-1\">\n            <li>✅ Text should be clearly visible (not transparent)</li>\n            <li>✅ Cursor should be visible when typing</li>\n            <li>✅ Syntax highlighting should work (if enabled)</li>\n            <li>✅ Editor should fill the entire screen</li>\n            <li>✅ Scrolling should work smoothly</li>\n            <li>✅ All toolbar buttons should be functional</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgErB,CAAC;AAEc,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAExC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;8BAIR,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8JAAA,CAAA,iBAAc;wBACb,OAAO;wBACP,UAAU,CAAC,QAAU,WAAW,SAAS;wBACzC,QAAO;wBACP,SAAQ;wBACR,iBAAiB;;;;;;;;;;;8BAIrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}]}